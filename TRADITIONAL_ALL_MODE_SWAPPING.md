# Traditional_All Mode with Mode Swapping

## Overview

The `traditional_all` mode in VibroML has been enhanced with **mode swapping** functionality to provide more comprehensive exploration of soft mode parameter space. This feature ensures that each soft mode gets a chance to be the primary mode in displacement generation, addressing the limitation where only the softest mode was used as the primary mode.

## Problem Addressed

In the original implementation, the traditional_all mode combined the softest mode (mode1) with each other soft mode (mode2) using `mode2_ratio_scales` from `default_settings.json`. However, with settings like `"mode2_ratio_scales": [-0.25, 0.0, 0.25]`, we never explored having only the second mode displacement by itself, since the ratio is always relative to mode1.

## Solution: Mode Swapping

For each pairing of `(softest_mode, other_soft_mode)`, the enhanced traditional_all mode now generates structures using **two approaches**:

1. **Original approach**: `softest_mode` as mode1, `other_soft_mode` as mode2
2. **Swapped approach**: `other_soft_mode` as mode1, `softest_mode` as mode2

## Implementation Details

### Subfolder Structure

For each pairing, two sets of subfolders are created:

```
iter_1/
├── pairing_1_Gamma_X/          # Original: Gamma as primary, X as secondary
│   ├── sample_1/
│   ├── sample_2/
│   └── ...
├── pairing_1_X_Gamma/          # Swapped: X as primary, Gamma as secondary
│   ├── sample_1/
│   ├── sample_2/
│   └── ...
├── pairing_2_Gamma_Y/          # Original: Gamma as primary, Y as secondary
├── pairing_2_Y_Gamma/          # Swapped: Y as primary, Gamma as secondary
└── ...
```

### Configuration Processing

Each pairing is processed with two configurations:

```python
configurations = [
    {
        'name': f"pairing_{idx}_{softest_mode.label}_{other_mode.label}",
        'modes_info': [softest_mode, other_mode],  # Original order
        'description': f"Original: {softest_mode.label} as primary, {other_mode.label} as secondary"
    },
    {
        'name': f"pairing_{idx}_{other_mode.label}_{softest_mode.label}",
        'modes_info': [other_mode, softest_mode],  # Swapped order
        'description': f"Swapped: {other_mode.label} as primary, {softest_mode.label} as secondary"
    }
]
```

### Parameter Grid

Each configuration uses the full parameter grid:
- **Displacement scales**: From `soft_mode_displacement_scales`
- **Cell scale factors**: From `cell_scale_factors`
- **Mode2 ratio scales**: From `mode2_ratio_scales`

This effectively **doubles** the number of structures explored in the traditional screening.

### Supercell Determination

The supercell size is determined based on the **primary mode's q-point** in each configuration:
- Original configuration: Uses softest mode's q-point
- Swapped configuration: Uses other mode's q-point

## Benefits

1. **Comprehensive Exploration**: Each soft mode gets to be the primary mode, ensuring no displacement patterns are missed
2. **Better Coverage**: Explores cases where the non-softest mode dominates the displacement
3. **Improved Convergence**: More likely to find the global minimum by exploring more parameter combinations
4. **Systematic Approach**: Maintains the structured grid search while expanding the search space

## Usage

The mode swapping is automatically enabled when using the `traditional_all` method:

```bash
python -m vibroml.main --cif structure.cif --method traditional_all --auto
```

## Output and Analysis

### Console Output
The enhanced mode provides detailed logging:
```
--- Processing Pairing 1/3: Gamma + X ---

  Configuration 1/2: Original: Gamma as primary, X as secondary
    Using commensurate supercell size (2, 1, 1) based on primary mode q-point [0.5, 0.0, 0.0]
      Sample 1: Mode1 Scale: 0.250, Mode2 Ratio: -0.250, Cell Transform: (-0.07, -0.07, -0.07, 0.0, 0.0, 0.0)
      ...
    Best result for Original: Gamma as primary, X as secondary: -2.345678 eV/atom

  Configuration 2/2: Swapped: X as primary, Gamma as secondary
    Using commensurate supercell size (2, 1, 1) based on primary mode q-point [0.5, 0.0, 0.0]
      Sample 1: Mode1 Scale: 0.250, Mode2 Ratio: -0.250, Cell Transform: (-0.07, -0.07, -0.07, 0.0, 0.0, 0.0)
      ...
    Best result for Swapped: X as primary, Gamma as secondary: -2.456789 eV/atom
```

### Summary Report
The final summary includes configuration information:
```
--- Traditional All Soft Mode Optimization Summary (with Mode Swapping) ---

Final Top Structures:
Rank   Energy (eV/atom)   Configuration                  Parameters
1      -2.456789          Swapped: X as primary, Gamma   D1:1.000, R21:0.250, Cell:(...)
2      -2.345678          Original: Gamma as primary, X  D1:0.500, R21:-0.250, Cell:(...)
...

Method: Traditional All with Mode Swapping
  - Softest mode combined with all other soft modes
  - Each pairing explored in both original and swapped configurations
  - Original: softest as primary, other as secondary
  - Swapped: other as primary, softest as secondary
```

## Performance Considerations

- **Computational Cost**: Approximately doubles the number of structures generated compared to the original traditional_all mode
- **Storage**: Requires more disk space due to additional subfolders and structures
- **Time**: Increases runtime proportionally to the number of additional configurations

## Compatibility

The mode swapping feature is:
- ✅ Fully backward compatible with existing VibroML infrastructure
- ✅ Uses existing parameter settings from `default_settings.json`
- ✅ Integrates with existing relaxation and analysis workflows
- ✅ Compatible with all supported calculators (MACE, M3GNet)

## Technical Implementation

The mode swapping is implemented in the `run_traditional_all_soft_mode_optimization` function in `vibroml/auto_optimize.py`. Key changes include:

1. **Configuration Array**: Each pairing generates two configurations with different mode orders
2. **Dynamic Subfolder Creation**: Subfolders are created based on configuration names
3. **Mode-Specific Supercell**: Supercell size determined by primary mode's q-point
4. **Enhanced Logging**: Detailed output showing both configurations
5. **Comprehensive Results**: All configurations included in final analysis and ranking
