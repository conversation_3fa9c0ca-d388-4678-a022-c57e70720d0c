#!/usr/bin/env python3
"""
Test script to verify both fixes are working correctly.
"""

import sys
import os
import tempfile
import time
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from vibroml.utils.genetic_algorithm import GeneticAlgorithm
from vibroml.utils.relaxation_utils import MinimumIterationOptimizer, EnergyVolumeStopper

def test_mutation_tracking_summary_generation():
    """Test that mutation tracking data is properly generated and formatted for summaries."""
    print("Testing mutation tracking summary generation...")
    
    # Create GA with tracked data
    tracked_k_points_data = {
        'soft_modes': [
            {
                'label': 'X',
                'coordinate': [0.5, 0.0, 0.0],
                'frequency': -2.5,
                'band_index': 0,
                'kpoint_index_in_path': 5
            },
            {
                'label': 'M',
                'coordinate': [0.5, 0.5, 0.0],
                'frequency': -1.8,
                'band_index': 1,
                'kpoint_index_in_path': 7
            }
        ],
        'highest_freq_modes': [],
        'lowest_freq_modes': []
    }
    
    ga = GeneticAlgorithm(
        population_size=10,
        mutation_rate=0.8,  # High mutation rate to ensure some replacements
        displacement_scale_bounds=(0.1, 2.0),
        ratio_mode2_to_mode1_bounds=(-1.0, 1.0),
        cell_scale_bounds=(-0.1, 0.1),
        cell_angle_bounds=(-5.0, 5.0),
        supercell_variants=[(2, 2, 2)],
        tracked_k_points_data=tracked_k_points_data
    )
    
    # Initialize population
    ga.initialize_population()
    
    # Get mutation summary
    summary = ga.get_mutation_summary()
    
    print(f"  ✓ Mutation summary generated successfully")
    print(f"    - Total individuals: {summary['total_individuals']}")
    print(f"    - Mode replacements: {summary['mode_replacements']}")
    print(f"    - Replacement rate: {summary['replacement_rate']:.2%}")
    print(f"    - Selected modes: {len(summary['selected_modes'])}")
    
    # Test summary formatting (simulate what would be written to file)
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
        # Simulate the exact condition and writing code from auto_optimize.py
        mutation_summary = summary
        if mutation_summary is not None and 'total_individuals' in mutation_summary:
            f.write(f"--- GA Mutation Tracking Summary ---\n")
            f.write(f"Total Individuals: {mutation_summary['total_individuals']}\n")
            f.write(f"Mode Replacements: {mutation_summary['mode_replacements']}\n")
            f.write(f"Replacement Rate: {mutation_summary['replacement_rate']:.2%}\n")
            if mutation_summary['selected_modes']:
                f.write(f"Selected Modes for Replacement:\n")
                for i, mode in enumerate(mutation_summary['selected_modes'][:5]):  # Show first 5
                    f.write(f"  {i+1}. {mode['label']} (freq: {mode['frequency']:.4f}, band: {mode['band_index']})\n")
                if len(mutation_summary['selected_modes']) > 5:
                    f.write(f"  ... and {len(mutation_summary['selected_modes']) - 5} more\n")
            else:
                f.write(f"No modes were selected for replacement in this generation.\n")
            f.write(f"\n")
        else:
            f.write("ERROR: Mutation summary condition failed!\n")
        
        temp_file = f.name
    
    # Read back and verify content
    with open(temp_file, 'r') as f:
        content = f.read()
    
    os.unlink(temp_file)
    
    print(f"  ✓ Summary file content:")
    print("    " + content.replace('\n', '\n    '))
    
    # Verify expected content is present
    assert "GA Mutation Tracking Summary" in content
    assert f"Total Individuals: {summary['total_individuals']}" in content
    assert f"Mode Replacements: {summary['mode_replacements']}" in content
    
    print("  ✓ Mutation tracking summary generation verified!")
    return True

def test_minimum_iteration_optimizer():
    """Test that minimum iteration enforcement is working."""
    print("Testing minimum iteration optimizer...")
    
    # Test MinimumIterationOptimizer class
    class MockOptimizer:
        def __init__(self):
            self.fmax = 0.05
            self.nsteps = None  # This is the key issue we're fixing
            self.converged_calls = 0
            
        def converged(self, forces=None):
            self.converged_calls += 1
            # Always return True to test that minimum iterations prevent early stopping
            return True
            
        def run(self, fmax=0.05, steps=None):
            self.fmax = fmax
            return "mock_run_complete"
            
        def attach(self, callback):
            pass
    
    mock_opt = MockOptimizer()
    min_iter_opt = MinimumIterationOptimizer(mock_opt, min_iterations=5)
    
    # Test that convergence is blocked for first few iterations
    print("    Testing convergence blocking:")
    for i in range(1, 8):
        result = min_iter_opt._custom_converged()
        if i < 5:
            assert result == False, f"Should not converge at iteration {i}"
            print(f"      ✓ Iteration {i}: Correctly blocked convergence")
        else:
            assert result == True, f"Should converge at iteration {i}"
            print(f"      ✓ Iteration {i}: Correctly allowed convergence")
    
    # Test the nsteps initialization fix
    print("    Testing nsteps initialization fix:")
    mock_opt_with_none_nsteps = MockOptimizer()
    assert mock_opt_with_none_nsteps.nsteps is None, "Mock should start with nsteps=None"
    
    min_iter_opt_2 = MinimumIterationOptimizer(mock_opt_with_none_nsteps, min_iterations=3)
    result = min_iter_opt_2.run(fmax=0.05)
    
    # Check that nsteps was initialized
    assert mock_opt_with_none_nsteps.nsteps == 0, f"nsteps should be initialized to 0, got {mock_opt_with_none_nsteps.nsteps}"
    print(f"      ✓ nsteps correctly initialized from None to {mock_opt_with_none_nsteps.nsteps}")
    
    print("  ✓ Minimum iteration optimizer verified!")
    return True

def main():
    """Run all tests."""
    print("Running comprehensive tests for both fixes...\n")
    
    try:
        test_mutation_tracking_summary_generation()
        print()
        test_minimum_iteration_optimizer()
        print()
        print("🎉 All tests passed! Both fixes are working correctly.")
        print("\n=== Summary of Fixes ===")
        print("1. ✅ Mutation tracking in population summaries:")
        print("   - Fixed variable scope and initialization issues")
        print("   - Ensured mutation summary is always available")
        print("   - Changed condition from 'if mutation_summary:' to explicit check")
        print()
        print("2. ✅ Premature relaxation termination:")
        print("   - Added MinimumIterationOptimizer wrapper class")
        print("   - Enforces minimum 5 iterations before convergence checks")
        print("   - Fixed nsteps initialization issue in ASE optimizers")
        print("   - Updated EnergyVolumeStopper to respect minimum iterations")
        return True
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
