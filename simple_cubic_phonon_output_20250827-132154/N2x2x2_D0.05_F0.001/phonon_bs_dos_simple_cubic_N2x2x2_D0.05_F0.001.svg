<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="484.233125pt" height="320.549375pt" viewBox="0 0 484.233125 320.549375" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-27T13:22:13.863796</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 320.549375 
L 484.233125 320.549375 
L 484.233125 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 48.633125 280.8 
L 386.313125 280.8 
L 386.313125 36 
L 48.633125 36 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="m4961632177" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m4961632177" x="48.633125" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- G -->
      <g transform="translate(45.53375 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#m4961632177" x="93.296968" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- X -->
      <g transform="translate(90.556968 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#m4961632177" x="137.960811" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- M -->
      <g transform="translate(134.509561 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#m4961632177" x="201.125024" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- G -->
      <g transform="translate(198.025649 293.87875) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-47"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#m4961632177" x="278.485069" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- R -->
      <g transform="translate(275.705694 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-52"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#m4961632177" x="341.649282" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- X|M -->
      <g transform="translate(334.110532 293.91375) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-7c" d="M 1344 4891 
L 1344 -1509 
L 813 -1509 
L 813 4891 
L 1344 4891 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-7c" x="68.505859"/>
       <use xlink:href="#DejaVuSans-4d" x="102.197266"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#m4961632177" x="386.313125" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- R -->
      <g transform="translate(383.53375 293.87875) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-52"/>
      </g>
     </g>
    </g>
    <g id="text_8">
     <!-- Wave vector -->
     <g transform="translate(173.829219 310.437812) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-57"/>
      <use xlink:href="#DejaVuSans-61" x="92.501953"/>
      <use xlink:href="#DejaVuSans-76" x="153.78125"/>
      <use xlink:href="#DejaVuSans-65" x="212.960938"/>
      <use xlink:href="#DejaVuSans-20" x="274.484375"/>
      <use xlink:href="#DejaVuSans-76" x="306.271484"/>
      <use xlink:href="#DejaVuSans-65" x="365.451172"/>
      <use xlink:href="#DejaVuSans-63" x="426.974609"/>
      <use xlink:href="#DejaVuSans-74" x="481.955078"/>
      <use xlink:href="#DejaVuSans-6f" x="521.164062"/>
      <use xlink:href="#DejaVuSans-72" x="582.345703"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_8">
      <defs>
       <path id="m0f1e362f2e" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m0f1e362f2e" x="48.633125" y="274.168657" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- −10 -->
      <g transform="translate(24.749375 277.208032) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
       <use xlink:href="#DejaVuSans-30" x="147.412109"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m0f1e362f2e" x="48.633125" y="237.972612" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- −5 -->
      <g transform="translate(29.839375 241.011987) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-35" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_10">
      <g>
       <use xlink:href="#m0f1e362f2e" x="48.633125" y="201.776567" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0 -->
      <g transform="translate(36.543125 204.815942) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_11">
      <g>
       <use xlink:href="#m0f1e362f2e" x="48.633125" y="165.580522" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 5 -->
      <g transform="translate(36.543125 168.619897) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_12">
      <g>
       <use xlink:href="#m0f1e362f2e" x="48.633125" y="129.384477" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 10 -->
      <g transform="translate(31.453125 132.423852) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-30" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_13">
      <g>
       <use xlink:href="#m0f1e362f2e" x="48.633125" y="93.188432" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 15 -->
      <g transform="translate(31.453125 96.227807) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-35" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_14">
      <g>
       <use xlink:href="#m0f1e362f2e" x="48.633125" y="56.992387" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 20 -->
      <g transform="translate(31.453125 60.031762) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
       <use xlink:href="#DejaVuSans-30" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="text_16">
     <!-- Frequency (THz) -->
     <g transform="translate(17.837813 215.462031) rotate(-90) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-72" x="50.269531"/>
      <use xlink:href="#DejaVuSans-65" x="89.132812"/>
      <use xlink:href="#DejaVuSans-71" x="150.65625"/>
      <use xlink:href="#DejaVuSans-75" x="214.132812"/>
      <use xlink:href="#DejaVuSans-65" x="277.511719"/>
      <use xlink:href="#DejaVuSans-6e" x="339.035156"/>
      <use xlink:href="#DejaVuSans-63" x="402.414062"/>
      <use xlink:href="#DejaVuSans-79" x="457.394531"/>
      <use xlink:href="#DejaVuSans-20" x="516.574219"/>
      <use xlink:href="#DejaVuSans-28" x="548.361328"/>
      <use xlink:href="#DejaVuSans-54" x="587.375"/>
      <use xlink:href="#DejaVuSans-48" x="648.458984"/>
      <use xlink:href="#DejaVuSans-7a" x="723.654297"/>
      <use xlink:href="#DejaVuSans-29" x="776.144531"/>
     </g>
    </g>
   </g>
   <g id="line2d_15">
    <path d="M 48.633125 231.917376 
L 52.355112 232.048542 
L 56.077099 232.427335 
L 59.799086 233.012218 
L 63.521073 233.741541 
L 67.24306 234.542188 
L 70.965047 235.337976 
L 74.687033 236.056756 
L 78.40902 236.63652 
L 82.131007 237.032346 
L 85.852994 237.228579 
L 89.574981 237.263372 
L 93.296968 237.247729 
L 97.018955 237.361976 
L 100.740942 237.714658 
L 104.462929 238.268699 
L 108.184916 238.978363 
L 111.906903 239.788091 
L 115.62889 240.638273 
L 119.350877 241.80158 
L 123.072863 243.155004 
L 126.79485 244.282838 
L 130.516837 245.128365 
L 134.238824 245.651545 
L 137.960811 245.828571 
L 141.469934 245.660326 
L 144.979057 245.167814 
L 148.48818 244.401305 
L 151.997303 243.494179 
L 155.506426 242.614564 
L 159.015549 241.756214 
L 162.524672 240.851613 
L 166.033795 239.879052 
L 169.542917 238.844837 
L 173.05204 237.769032 
L 176.561163 236.680384 
L 180.070286 235.614036 
L 183.579409 234.609686 
L 187.088532 233.709437 
L 190.597655 232.955042 
L 194.106778 232.384582 
L 197.615901 232.028874 
L 201.125024 231.917376 
L 204.488504 229.787373 
L 207.851984 223.729854 
L 211.215465 213.982345 
L 214.578945 188.877005 
L 217.942425 179.70799 
L 221.305905 173.14165 
L 224.669385 167.732244 
L 228.032866 163.523995 
L 231.396346 160.616497 
L 234.759826 158.422719 
L 238.123306 156.938844 
L 241.486787 156.104344 
L 244.850267 155.734699 
L 248.213747 155.470678 
L 251.577227 154.824977 
L 254.940708 153.356816 
L 258.304188 150.871569 
L 261.667668 147.49221 
L 265.031148 143.56798 
L 268.394629 139.519802 
L 271.758109 135.721184 
L 275.121589 132.4465 
L 278.485069 130.21856 
L 281.64328 132.012481 
L 284.801491 134.793049 
L 287.959701 138.106306 
L 291.117912 141.773616 
L 294.276122 145.569797 
L 297.434333 149.265554 
L 300.592544 152.720934 
L 303.750754 155.988169 
L 306.908965 159.329075 
L 310.067176 163.097875 
L 313.225386 167.609726 
L 316.383597 174.266095 
L 319.541807 182.974728 
L 322.700018 206.964583 
L 325.858229 221.105476 
L 329.016439 227.769668 
L 332.17465 232.153875 
L 335.332861 235.036132 
L 338.491071 236.692256 
L 341.649282 237.247729 
L 341.649282 245.828571 
L 345.084962 244.648261 
L 348.520642 240.970364 
L 351.956323 234.220905 
L 355.392003 222.161036 
L 358.827683 183.387837 
L 362.263363 167.650622 
L 365.699044 156.879482 
L 369.134724 148.464439 
L 372.570404 141.811119 
L 376.006084 136.715341 
L 379.441765 133.548547 
L 382.877445 131.784786 
L 386.313125 130.21856 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_16">
    <path d="M 48.633125 231.917376 
L 52.355112 232.029785 
L 56.077099 232.408992 
L 59.799086 232.99455 
L 63.521073 233.724717 
L 67.24306 234.526296 
L 70.965047 235.323024 
L 74.687033 236.042675 
L 78.40902 236.623152 
L 82.131007 237.019416 
L 85.852994 237.215662 
L 89.574981 237.249993 
L 93.296968 237.233966 
L 97.018955 235.55537 
L 100.740942 234.763897 
L 104.462929 235.84648 
L 108.184916 237.207726 
L 111.906903 238.729786 
L 115.62889 240.296315 
L 119.350877 241.470249 
L 123.072863 242.23013 
L 126.79485 242.871442 
L 130.516837 243.356832 
L 134.238824 243.659082 
L 137.960811 243.761676 
L 141.469934 243.677381 
L 144.979057 243.41532 
L 148.48818 242.934388 
L 151.997303 242.116539 
L 155.506426 240.81522 
L 159.015549 239.064546 
L 162.524672 236.96621 
L 166.033795 234.582333 
L 169.542917 231.953067 
L 173.05204 229.110501 
L 176.561163 226.083161 
L 180.070286 222.897436 
L 183.579409 219.578142 
L 187.088532 216.148888 
L 190.597655 212.632449 
L 194.106778 221.62373 
L 197.615901 229.331978 
L 201.125024 231.917376 
L 204.488504 229.787373 
L 207.851984 223.729854 
L 211.215465 213.982345 
L 214.578945 188.877005 
L 217.942425 179.70799 
L 221.305905 173.14165 
L 224.669385 167.732244 
L 228.032866 163.125397 
L 231.396346 159.184677 
L 234.759826 155.843526 
L 238.123306 153.067269 
L 241.486787 150.839926 
L 244.850267 149.142023 
L 248.213747 147.887587 
L 251.577227 146.832363 
L 254.940708 145.587018 
L 258.304188 143.820412 
L 261.667668 141.458064 
L 265.031148 138.692296 
L 268.394629 135.858222 
L 271.758109 133.297511 
L 275.121589 131.278698 
L 278.485069 130.214809 
L 281.64328 131.778939 
L 284.801491 133.944726 
L 287.959701 136.359021 
L 291.117912 139.017532 
L 294.276122 141.934206 
L 297.434333 145.135233 
L 300.592544 148.660478 
L 303.750754 152.56605 
L 306.908965 156.928996 
L 310.067176 161.858618 
L 313.225386 167.526989 
L 316.383597 173.149424 
L 319.541807 180.209014 
L 322.700018 190.817395 
L 325.858229 215.925184 
L 329.016439 225.222782 
L 332.17465 230.959355 
L 335.332861 234.562185 
L 338.491071 236.590066 
L 341.649282 237.233966 
L 341.649282 243.761676 
L 345.084962 242.529504 
L 348.520642 238.666733 
L 351.956323 231.448553 
L 355.392003 217.503985 
L 358.827683 179.381279 
L 362.263363 165.406914 
L 365.699044 155.209077 
L 369.134724 147.098109 
L 372.570404 140.632197 
L 376.006084 135.657703 
L 379.441765 133.54685 
L 382.877445 131.78363 
L 386.313125 130.214809 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_17">
    <path d="M 48.633125 231.888948 
L 52.355112 227.306323 
L 56.077099 209.618745 
L 59.799086 213.381583 
L 63.521073 216.979246 
L 67.24306 220.358382 
L 70.965047 223.470536 
L 74.687033 226.27129 
L 78.40902 228.719025 
L 82.131007 230.771208 
L 85.852994 232.374177 
L 89.574981 233.439608 
L 93.296968 233.825605 
L 97.018955 234.064763 
L 100.740942 230.079525 
L 104.462929 218.148665 
L 108.184916 181.557768 
L 111.906903 167.673456 
L 115.62889 158.016817 
L 119.350877 150.966424 
L 123.072863 146.1099 
L 126.79485 143.080551 
L 130.516837 141.396909 
L 134.238824 140.593515 
L 137.960811 140.3619 
L 141.469934 139.975766 
L 144.979057 138.989293 
L 148.48818 137.842274 
L 151.997303 137.055367 
L 155.506426 137.035278 
L 159.015549 137.983406 
L 162.524672 139.934736 
L 166.033795 142.847087 
L 169.542917 146.664727 
L 173.05204 151.346058 
L 176.561163 156.876765 
L 180.070286 163.293579 
L 183.579409 170.764846 
L 187.088532 179.943282 
L 190.597655 196.265832 
L 194.106778 217.497412 
L 197.615901 228.664078 
L 201.125024 231.888948 
L 204.488504 229.177842 
L 207.851984 220.380965 
L 211.215465 194.129686 
L 214.578945 182.981516 
L 217.942425 176.606648 
L 221.305905 171.468297 
L 224.669385 167.137358 
L 228.032866 163.125397 
L 231.396346 159.184677 
L 234.759826 155.843526 
L 238.123306 153.067269 
L 241.486787 150.839926 
L 244.850267 149.142023 
L 248.213747 147.887587 
L 251.577227 146.832363 
L 254.940708 145.587018 
L 258.304188 143.820412 
L 261.667668 141.458064 
L 265.031148 138.692296 
L 268.394629 135.858222 
L 271.758109 133.297511 
L 275.121589 131.278698 
L 278.485069 130.214809 
L 281.64328 130.465341 
L 284.801491 131.217091 
L 287.959701 132.470571 
L 291.117912 134.226786 
L 294.276122 136.487537 
L 297.434333 139.256004 
L 300.592544 142.537841 
L 303.750754 146.343196 
L 306.908965 150.690539 
L 310.067176 155.614204 
L 313.225386 161.180471 
L 316.383597 167.526592 
L 319.541807 174.980754 
L 322.700018 186.025719 
L 325.858229 215.161711 
L 329.016439 223.728539 
L 332.17465 228.48579 
L 335.332861 231.532908 
L 338.491071 233.262695 
L 341.649282 233.825605 
L 341.649282 140.3619 
L 345.084962 140.339469 
L 348.520642 140.267191 
L 351.956323 140.130564 
L 355.392003 139.906889 
L 358.827683 139.567447 
L 362.263363 139.080423 
L 365.699044 138.414385 
L 369.134724 137.542019 
L 372.570404 136.443735 
L 376.006084 135.110835 
L 379.441765 133.099908 
L 382.877445 130.936528 
L 386.313125 130.214809 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_18">
    <path d="M 48.633125 202.520029 
L 52.355112 205.753942 
L 56.077099 209.586357 
L 59.799086 213.3607 
L 63.521073 216.964244 
L 67.24306 220.34703 
L 70.965047 223.461686 
L 74.687033 226.264232 
L 78.40902 228.713228 
L 82.131007 230.766172 
L 85.852994 232.369337 
L 89.574981 233.4344 
L 93.296968 233.82004 
L 97.018955 231.935767 
L 100.740942 225.580096 
L 104.462929 206.802489 
L 108.184916 175.889053 
L 111.906903 163.575213 
L 115.62889 154.168679 
L 119.350877 147.001979 
L 123.072863 142.133821 
L 126.79485 139.711988 
L 130.516837 139.376413 
L 134.238824 139.994039 
L 137.960811 140.359522 
L 141.469934 139.924214 
L 144.979057 138.796281 
L 148.48818 137.434895 
L 151.997303 136.394375 
L 155.506426 136.120786 
L 159.015549 136.841311 
L 162.524672 138.592877 
L 166.033795 141.318457 
L 169.542917 144.940589 
L 173.05204 149.39172 
L 176.561163 154.622181 
L 180.070286 160.608394 
L 183.579409 167.388592 
L 187.088532 175.229762 
L 190.597655 185.826959 
L 194.106778 209.051073 
L 197.615901 205.427957 
L 201.125024 202.520029 
L 204.488504 194.392696 
L 207.851984 185.669668 
L 211.215465 173.558436 
L 214.578945 160.861924 
L 217.942425 150.037556 
L 221.305905 141.259997 
L 224.669385 134.48085 
L 228.032866 129.828051 
L 231.396346 127.284215 
L 234.759826 126.338194 
L 238.123306 126.654679 
L 241.486787 127.780565 
L 244.850267 129.179479 
L 248.213747 130.335642 
L 251.577227 130.925879 
L 254.940708 130.923022 
L 258.304188 130.504615 
L 261.667668 129.89314 
L 265.031148 129.279445 
L 268.394629 128.818127 
L 271.758109 128.641633 
L 275.121589 128.858882 
L 278.485069 129.295224 
L 281.64328 129.529779 
L 284.801491 130.235361 
L 287.959701 131.417663 
L 291.117912 133.086025 
L 294.276122 135.253337 
L 297.434333 137.936135 
L 300.592544 141.155245 
L 303.750754 144.937533 
L 306.908965 149.319911 
L 310.067176 154.358127 
L 313.225386 160.146776 
L 316.383597 166.870456 
L 319.541807 174.965943 
L 322.700018 184.595308 
L 325.858229 210.971234 
L 329.016439 222.043722 
L 332.17465 227.747137 
L 335.332861 231.244033 
L 338.491071 233.191338 
L 341.649282 233.82004 
L 341.649282 140.359522 
L 345.084962 140.337092 
L 348.520642 140.264821 
L 351.956323 140.128205 
L 355.392003 139.904547 
L 358.827683 139.56513 
L 362.263363 139.078142 
L 365.699044 138.412151 
L 369.134724 137.539849 
L 372.570404 136.441652 
L 376.006084 135.108846 
L 379.441765 132.119054 
L 382.877445 129.998538 
L 386.313125 129.295224 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_19">
    <path d="M 48.633125 201.296202 
L 52.355112 205.688217 
L 56.077099 198.334973 
L 59.799086 172.954157 
L 63.521073 160.027608 
L 67.24306 149.269376 
L 70.965047 139.893319 
L 74.687033 131.804581 
L 78.40902 125.047163 
L 82.131007 119.693916 
L 85.852994 115.814634 
L 89.574981 113.46407 
L 93.296968 112.676647 
L 97.018955 112.778844 
L 100.740942 113.066663 
L 104.462929 113.482436 
L 108.184916 113.925932 
L 111.906903 114.250896 
L 115.62889 114.271876 
L 119.350877 113.807609 
L 123.072863 112.789704 
L 126.79485 111.383296 
L 130.516837 109.962182 
L 134.238824 108.927115 
L 137.960811 108.551992 
L 141.469934 108.476402 
L 144.979057 108.239856 
L 148.48818 107.814774 
L 151.997303 107.22523 
L 155.506426 106.627708 
L 159.015549 106.344202 
L 162.524672 106.797004 
L 166.033795 108.400506 
L 169.542917 111.48796 
L 173.05204 116.288082 
L 176.561163 122.930705 
L 180.070286 131.46515 
L 183.579409 141.886194 
L 187.088532 154.159226 
L 190.597655 168.089623 
L 194.106778 181.818691 
L 197.615901 192.508911 
L 201.125024 201.296202 
L 204.488504 192.429509 
L 207.851984 181.918925 
L 211.215465 170.109719 
L 214.578945 158.649617 
L 217.942425 148.649374 
L 221.305905 140.458066 
L 224.669385 134.18483 
L 228.032866 129.828051 
L 231.396346 127.284215 
L 234.759826 126.338194 
L 238.123306 126.654679 
L 241.486787 127.780565 
L 244.850267 129.179479 
L 248.213747 130.335642 
L 251.577227 130.925879 
L 254.940708 130.923022 
L 258.304188 130.504615 
L 261.667668 129.89314 
L 265.031148 129.279445 
L 268.394629 128.818127 
L 271.758109 128.641633 
L 275.121589 128.858882 
L 278.485069 129.295224 
L 281.64328 128.125458 
L 284.801491 127.172814 
L 287.959701 126.710623 
L 291.117912 126.622184 
L 294.276122 126.724833 
L 297.434333 126.758715 
L 300.592544 126.369533 
L 303.750754 125.129587 
L 306.908965 122.647122 
L 310.067176 118.962354 
L 313.225386 118.160881 
L 316.383597 117.328864 
L 319.541807 116.480149 
L 322.700018 115.640002 
L 325.858229 114.841553 
L 329.016439 114.121639 
L 332.17465 113.516663 
L 335.332861 113.05891 
L 338.491071 112.77358 
L 341.649282 112.676647 
L 341.649282 108.551992 
L 345.084962 108.729646 
L 348.520642 109.258471 
L 351.956323 110.126122 
L 355.392003 111.312235 
L 358.827683 112.788754 
L 362.263363 114.520407 
L 365.699044 116.465355 
L 369.134724 118.576025 
L 372.570404 120.800142 
L 376.006084 123.081845 
L 379.441765 125.362244 
L 382.877445 127.573353 
L 386.313125 129.295224 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_20">
    <path d="M 48.633125 201.296202 
L 52.355112 188.135812 
L 56.077099 173.158395 
L 59.799086 156.360566 
L 63.521073 139.822764 
L 67.24306 124.611964 
L 70.965047 110.997221 
L 74.687033 99.14425 
L 78.40902 89.205693 
L 82.131007 81.319328 
L 85.852994 75.59988 
L 89.574981 72.132993 
L 93.296968 70.971429 
L 97.018955 71.657688 
L 100.740942 73.68626 
L 104.462929 76.964205 
L 108.184916 81.328131 
L 111.906903 86.528606 
L 115.62889 92.202638 
L 119.350877 97.833206 
L 123.072863 102.727753 
L 126.79485 106.167801 
L 130.516837 107.906359 
L 134.238824 108.462641 
L 137.960811 108.550235 
L 141.469934 108.445567 
L 144.979057 108.113494 
L 148.48818 107.540396 
L 151.997303 106.774815 
L 155.506426 106.006359 
L 159.015549 105.580222 
L 162.524672 105.921134 
L 166.033795 107.431623 
L 169.542917 110.428735 
L 173.05204 115.124404 
L 176.561163 121.629239 
L 180.070286 129.964635 
L 183.579409 140.075329 
L 187.088532 151.828277 
L 190.597655 164.922646 
L 194.106778 178.442861 
L 197.615901 190.748066 
L 201.125024 201.296202 
L 204.488504 192.429509 
L 207.851984 181.918925 
L 211.215465 170.109719 
L 214.578945 158.649617 
L 217.942425 148.649374 
L 221.305905 140.458066 
L 224.669385 134.18483 
L 228.032866 129.608901 
L 231.396346 126.477446 
L 234.759826 124.820959 
L 238.123306 124.275588 
L 241.486787 124.414256 
L 244.850267 124.82328 
L 248.213747 125.196401 
L 251.577227 125.388327 
L 254.940708 125.396286 
L 258.304188 125.302926 
L 261.667668 125.227976 
L 265.031148 125.303384 
L 268.394629 125.66358 
L 271.758109 126.439222 
L 275.121589 127.741375 
L 278.485069 129.28511 
L 281.64328 127.944692 
L 284.801491 126.433778 
L 287.959701 125.10305 
L 291.117912 123.946981 
L 294.276122 122.941703 
L 297.434333 122.054872 
L 300.592544 121.249258 
L 303.750754 120.486756 
L 306.908965 119.733066 
L 310.067176 118.738585 
L 313.225386 113.52237 
L 316.383597 107.353476 
L 319.541807 100.687831 
L 322.700018 93.979962 
L 325.858229 87.634677 
L 329.016439 81.991926 
L 332.17465 77.325294 
L 335.332861 73.844316 
L 338.491071 71.696973 
L 341.649282 70.971429 
L 341.649282 108.550235 
L 345.084962 108.727885 
L 348.520642 109.256697 
L 351.956323 110.124328 
L 355.392003 111.310411 
L 358.827683 112.786891 
L 362.263363 114.518495 
L 365.699044 116.46338 
L 369.134724 118.573973 
L 372.570404 120.797989 
L 376.006084 123.079553 
L 379.441765 125.359724 
L 382.877445 127.570274 
L 386.313125 129.28511 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_21">
    <path d="M 48.633125 280.8 
L 48.633125 36 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_22">
    <path d="M 93.296968 280.8 
L 93.296968 36 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_23">
    <path d="M 137.960811 280.8 
L 137.960811 36 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_24">
    <path d="M 201.125024 280.8 
L 201.125024 36 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_25">
    <path d="M 278.485069 280.8 
L 278.485069 36 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_26">
    <path d="M 341.649282 280.8 
L 341.649282 36 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_27">
    <path d="M 341.649282 280.8 
L 341.649282 36 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_28">
    <path d="M 386.313125 280.8 
L 386.313125 36 
" clip-path="url(#p9b1943e2e1)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="patch_3">
    <path d="M 48.633125 280.8 
L 48.633125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 386.313125 280.8 
L 386.313125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 48.633125 280.8 
L 386.313125 280.8 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 48.633125 36 
L 386.313125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_7">
    <path d="M 391.353125 280.8 
L 477.033125 280.8 
L 477.033125 36 
L 391.353125 36 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m7cc595d616" d="M 395.252922 -118.772808 
L 395.252922 -71.17833 
L 398.529209 -72.076617 
L 399.241506 -72.974903 
L 402.108747 -73.87319 
L 404.522233 -74.771477 
L 409.093324 -75.669764 
L 415.869317 -76.56805 
L 425.921589 -77.466337 
L 436.921885 -78.364624 
L 436.529762 -79.262911 
L 440.669032 -80.161198 
L 451.532605 -81.059484 
L 454.793877 -81.957771 
L 447.210833 -82.856058 
L 448.425696 -83.754345 
L 440.269721 -84.652631 
L 444.14422 -85.550918 
L 446.565511 -86.449205 
L 444.752524 -87.347492 
L 442.623475 -88.245778 
L 438.965158 -89.144065 
L 438.335814 -90.042352 
L 439.025414 -90.940639 
L 434.989829 -91.838925 
L 427.507156 -92.737212 
L 428.131093 -93.635499 
L 428.347007 -94.533786 
L 428.798467 -95.432073 
L 425.018918 -96.330359 
L 422.221461 -97.228646 
L 419.034351 -98.126933 
L 418.924459 -99.02522 
L 418.588668 -99.923506 
L 412.837755 -100.821793 
L 414.570666 -101.72008 
L 417.703883 -102.618367 
L 414.913003 -103.516653 
L 414.302143 -104.41494 
L 414.647927 -105.313227 
L 411.26546 -106.211514 
L 408.966886 -107.1098 
L 404.831017 -108.008087 
L 407.386047 -108.906374 
L 406.943224 -109.804661 
L 404.267509 -110.702948 
L 404.895304 -111.601234 
L 404.279498 -112.499521 
L 401.655669 -113.397808 
L 400.693677 -114.296095 
L 400.434328 -115.194381 
L 398.534362 -116.092668 
L 396.520642 -116.990955 
L 395.79006 -117.889242 
L 395.24767 -118.787528 
L 396.950007 -119.685815 
L 397.42485 -120.584102 
L 397.948793 -121.482389 
L 399.641362 -122.380675 
L 399.81531 -123.278962 
L 401.999421 -124.177249 
L 401.572819 -125.075536 
L 402.684358 -125.973823 
L 404.414992 -126.872109 
L 405.960035 -127.770396 
L 407.31163 -128.668683 
L 407.258333 -129.56697 
L 411.402018 -130.465256 
L 408.893395 -131.363543 
L 410.351359 -132.26183 
L 408.98472 -133.160117 
L 410.92457 -134.058403 
L 413.709196 -134.95669 
L 412.930953 -135.854977 
L 410.139064 -136.753264 
L 411.130577 -137.65155 
L 414.602658 -138.549837 
L 416.036225 -139.448124 
L 416.819529 -140.346411 
L 420.460579 -141.244698 
L 421.204226 -142.142984 
L 415.995001 -143.041271 
L 418.360053 -143.939558 
L 416.11301 -144.837845 
L 414.072892 -145.736131 
L 421.450571 -146.634418 
L 423.56028 -147.532705 
L 422.954026 -148.430992 
L 420.553814 -149.329278 
L 426.02701 -150.227565 
L 423.736667 -151.125852 
L 423.573855 -152.024139 
L 419.944084 -152.922425 
L 420.835729 -153.820712 
L 419.736136 -154.718999 
L 431.067861 -155.617286 
L 428.739752 -156.515573 
L 433.89593 -157.413859 
L 426.939799 -158.312146 
L 426.264274 -159.210433 
L 418.444944 -160.10872 
L 428.060033 -161.007006 
L 441.953194 -161.905293 
L 437.655409 -162.80358 
L 437.393701 -163.701867 
L 427.952873 -164.600153 
L 425.477808 -165.49844 
L 438.859066 -166.396727 
L 439.956145 -167.295014 
L 437.776528 -168.1933 
L 428.330451 -169.091587 
L 441.846272 -169.989874 
L 438.147195 -170.888161 
L 445.063294 -171.786448 
L 443.881958 -172.684734 
L 440.545208 -173.583021 
L 443.519889 -174.481308 
L 455.188978 -175.379595 
L 450.299328 -176.277881 
L 453.258754 -177.176168 
L 458.924107 -178.074455 
L 462.010359 -178.972742 
L 465.083602 -179.871028 
L 473.13858 -180.769315 
L 463.424502 -181.667602 
L 456.538928 -182.565889 
L 451.045472 -183.464175 
L 444.158322 -184.362462 
L 438.259292 -185.260749 
L 418.921596 -186.159036 
L 415.414651 -187.057323 
L 416.273609 -187.955609 
L 416.190225 -188.853896 
L 418.76302 -189.752183 
L 411.369615 -190.65047 
L 412.20067 -191.548756 
L 415.77775 -192.447043 
L 421.416815 -193.34533 
L 424.510579 -194.243617 
L 424.407123 -195.141903 
L 437.627315 -196.04019 
L 435.196417 -196.938477 
L 436.49699 -197.836764 
L 442.445162 -198.73505 
L 456.121491 -199.633337 
L 443.750039 -200.531624 
L 449.552485 -201.429911 
L 457.962886 -202.328198 
L 456.327342 -203.226484 
L 462.650645 -204.124771 
L 465.449429 -205.023058 
L 471.94295 -205.921345 
L 452.606701 -206.819631 
L 439.988769 -207.717918 
L 429.712341 -208.616205 
L 432.102141 -209.514492 
L 429.475511 -210.412778 
L 431.85953 -211.311065 
L 423.606564 -212.209352 
L 420.886548 -213.107639 
L 416.224361 -214.005925 
L 413.050316 -214.904212 
L 412.341316 -215.802499 
L 413.830594 -216.700786 
L 412.883257 -217.599073 
L 409.788497 -218.497359 
L 409.211179 -219.395646 
L 406.942524 -220.293933 
L 405.955145 -221.19222 
L 408.217934 -222.090506 
L 406.264669 -222.988793 
L 406.83949 -223.88708 
L 408.064957 -224.785367 
L 407.355227 -225.683653 
L 404.667116 -226.58194 
L 405.037691 -227.480227 
L 404.828303 -228.378514 
L 405.69072 -229.2768 
L 405.441122 -230.175087 
L 406.387292 -231.073374 
L 402.22351 -231.971661 
L 404.341521 -232.869948 
L 404.021944 -233.768234 
L 403.224921 -234.666521 
L 402.727227 -235.564808 
L 401.523281 -236.463095 
L 401.816746 -237.361381 
L 401.314472 -238.259668 
L 401.62794 -239.157955 
L 400.557613 -240.056242 
L 400.744532 -240.954528 
L 401.93001 -241.852815 
L 398.771743 -242.751102 
L 399.73797 -243.649389 
L 398.496594 -244.547675 
L 399.802339 -245.445962 
L 397.908334 -246.344249 
L 398.717885 -247.242536 
L 397.619089 -248.140823 
L 396.550851 -249.039109 
L 395.252295 -249.937396 
L 395.252295 -118.772808 
L 395.252295 -118.772808 
L 396.550851 -118.772808 
L 397.619089 -118.772808 
L 398.717885 -118.772808 
L 397.908334 -118.772808 
L 399.802339 -118.772808 
L 398.496594 -118.772808 
L 399.73797 -118.772808 
L 398.771743 -118.772808 
L 401.93001 -118.772808 
L 400.744532 -118.772808 
L 400.557613 -118.772808 
L 401.62794 -118.772808 
L 401.314472 -118.772808 
L 401.816746 -118.772808 
L 401.523281 -118.772808 
L 402.727227 -118.772808 
L 403.224921 -118.772808 
L 404.021944 -118.772808 
L 404.341521 -118.772808 
L 402.22351 -118.772808 
L 406.387292 -118.772808 
L 405.441122 -118.772808 
L 405.69072 -118.772808 
L 404.828303 -118.772808 
L 405.037691 -118.772808 
L 404.667116 -118.772808 
L 407.355227 -118.772808 
L 408.064957 -118.772808 
L 406.83949 -118.772808 
L 406.264669 -118.772808 
L 408.217934 -118.772808 
L 405.955145 -118.772808 
L 406.942524 -118.772808 
L 409.211179 -118.772808 
L 409.788497 -118.772808 
L 412.883257 -118.772808 
L 413.830594 -118.772808 
L 412.341316 -118.772808 
L 413.050316 -118.772808 
L 416.224361 -118.772808 
L 420.886548 -118.772808 
L 423.606564 -118.772808 
L 431.85953 -118.772808 
L 429.475511 -118.772808 
L 432.102141 -118.772808 
L 429.712341 -118.772808 
L 439.988769 -118.772808 
L 452.606701 -118.772808 
L 471.94295 -118.772808 
L 465.449429 -118.772808 
L 462.650645 -118.772808 
L 456.327342 -118.772808 
L 457.962886 -118.772808 
L 449.552485 -118.772808 
L 443.750039 -118.772808 
L 456.121491 -118.772808 
L 442.445162 -118.772808 
L 436.49699 -118.772808 
L 435.196417 -118.772808 
L 437.627315 -118.772808 
L 424.407123 -118.772808 
L 424.510579 -118.772808 
L 421.416815 -118.772808 
L 415.77775 -118.772808 
L 412.20067 -118.772808 
L 411.369615 -118.772808 
L 418.76302 -118.772808 
L 416.190225 -118.772808 
L 416.273609 -118.772808 
L 415.414651 -118.772808 
L 418.921596 -118.772808 
L 438.259292 -118.772808 
L 444.158322 -118.772808 
L 451.045472 -118.772808 
L 456.538928 -118.772808 
L 463.424502 -118.772808 
L 473.13858 -118.772808 
L 465.083602 -118.772808 
L 462.010359 -118.772808 
L 458.924107 -118.772808 
L 453.258754 -118.772808 
L 450.299328 -118.772808 
L 455.188978 -118.772808 
L 443.519889 -118.772808 
L 440.545208 -118.772808 
L 443.881958 -118.772808 
L 445.063294 -118.772808 
L 438.147195 -118.772808 
L 441.846272 -118.772808 
L 428.330451 -118.772808 
L 437.776528 -118.772808 
L 439.956145 -118.772808 
L 438.859066 -118.772808 
L 425.477808 -118.772808 
L 427.952873 -118.772808 
L 437.393701 -118.772808 
L 437.655409 -118.772808 
L 441.953194 -118.772808 
L 428.060033 -118.772808 
L 418.444944 -118.772808 
L 426.264274 -118.772808 
L 426.939799 -118.772808 
L 433.89593 -118.772808 
L 428.739752 -118.772808 
L 431.067861 -118.772808 
L 419.736136 -118.772808 
L 420.835729 -118.772808 
L 419.944084 -118.772808 
L 423.573855 -118.772808 
L 423.736667 -118.772808 
L 426.02701 -118.772808 
L 420.553814 -118.772808 
L 422.954026 -118.772808 
L 423.56028 -118.772808 
L 421.450571 -118.772808 
L 414.072892 -118.772808 
L 416.11301 -118.772808 
L 418.360053 -118.772808 
L 415.995001 -118.772808 
L 421.204226 -118.772808 
L 420.460579 -118.772808 
L 416.819529 -118.772808 
L 416.036225 -118.772808 
L 414.602658 -118.772808 
L 411.130577 -118.772808 
L 410.139064 -118.772808 
L 412.930953 -118.772808 
L 413.709196 -118.772808 
L 410.92457 -118.772808 
L 408.98472 -118.772808 
L 410.351359 -118.772808 
L 408.893395 -118.772808 
L 411.402018 -118.772808 
L 407.258333 -118.772808 
L 407.31163 -118.772808 
L 405.960035 -118.772808 
L 404.414992 -118.772808 
L 402.684358 -118.772808 
L 401.572819 -118.772808 
L 401.999421 -118.772808 
L 399.81531 -118.772808 
L 399.641362 -118.772808 
L 397.948793 -118.772808 
L 397.42485 -118.772808 
L 396.950007 -118.772808 
L 395.24767 -118.772808 
L 395.79006 -118.772808 
L 396.520642 -118.772808 
L 398.534362 -118.772808 
L 400.434328 -118.772808 
L 400.693677 -118.772808 
L 401.655669 -118.772808 
L 404.279498 -118.772808 
L 404.895304 -118.772808 
L 404.267509 -118.772808 
L 406.943224 -118.772808 
L 407.386047 -118.772808 
L 404.831017 -118.772808 
L 408.966886 -118.772808 
L 411.26546 -118.772808 
L 414.647927 -118.772808 
L 414.302143 -118.772808 
L 414.913003 -118.772808 
L 417.703883 -118.772808 
L 414.570666 -118.772808 
L 412.837755 -118.772808 
L 418.588668 -118.772808 
L 418.924459 -118.772808 
L 419.034351 -118.772808 
L 422.221461 -118.772808 
L 425.018918 -118.772808 
L 428.798467 -118.772808 
L 428.347007 -118.772808 
L 428.131093 -118.772808 
L 427.507156 -118.772808 
L 434.989829 -118.772808 
L 439.025414 -118.772808 
L 438.335814 -118.772808 
L 438.965158 -118.772808 
L 442.623475 -118.772808 
L 444.752524 -118.772808 
L 446.565511 -118.772808 
L 444.14422 -118.772808 
L 440.269721 -118.772808 
L 448.425696 -118.772808 
L 447.210833 -118.772808 
L 454.793877 -118.772808 
L 451.532605 -118.772808 
L 440.669032 -118.772808 
L 436.529762 -118.772808 
L 436.921885 -118.772808 
L 425.921589 -118.772808 
L 415.869317 -118.772808 
L 409.093324 -118.772808 
L 404.522233 -118.772808 
L 402.108747 -118.772808 
L 399.241506 -118.772808 
L 398.529209 -118.772808 
L 395.252922 -118.772808 
z
" style="stroke: #000000"/>
    </defs>
    <g clip-path="url(#p1cdeb1b793)">
     <use xlink:href="#m7cc595d616" x="0" y="320.549375" style="fill: #808080; stroke: #000000"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="text_17">
     <!-- DOS -->
     <g transform="translate(418.848906 295.437812) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-44"/>
      <use xlink:href="#DejaVuSans-4f" x="77.001953"/>
      <use xlink:href="#DejaVuSans-53" x="155.712891"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4"/>
   <g id="patch_8">
    <path d="M 391.353125 280.8 
L 391.353125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 477.033125 280.8 
L 477.033125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 391.353125 280.8 
L 477.033125 280.8 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_11">
    <path d="M 391.353125 36 
L 477.033125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
  <g id="text_18">
   <!-- Phonon band structure and DOS of FLi (simple_cubic) with (2x2x2) supercell -->
   <g transform="translate(11.29625 16.318125) scale(0.12 -0.12)">
    <defs>
     <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#DejaVuSans-50"/>
    <use xlink:href="#DejaVuSans-68" x="60.302734"/>
    <use xlink:href="#DejaVuSans-6f" x="123.681641"/>
    <use xlink:href="#DejaVuSans-6e" x="184.863281"/>
    <use xlink:href="#DejaVuSans-6f" x="248.242188"/>
    <use xlink:href="#DejaVuSans-6e" x="309.423828"/>
    <use xlink:href="#DejaVuSans-20" x="372.802734"/>
    <use xlink:href="#DejaVuSans-62" x="404.589844"/>
    <use xlink:href="#DejaVuSans-61" x="468.066406"/>
    <use xlink:href="#DejaVuSans-6e" x="529.345703"/>
    <use xlink:href="#DejaVuSans-64" x="592.724609"/>
    <use xlink:href="#DejaVuSans-20" x="656.201172"/>
    <use xlink:href="#DejaVuSans-73" x="687.988281"/>
    <use xlink:href="#DejaVuSans-74" x="740.087891"/>
    <use xlink:href="#DejaVuSans-72" x="779.296875"/>
    <use xlink:href="#DejaVuSans-75" x="820.410156"/>
    <use xlink:href="#DejaVuSans-63" x="883.789062"/>
    <use xlink:href="#DejaVuSans-74" x="938.769531"/>
    <use xlink:href="#DejaVuSans-75" x="977.978516"/>
    <use xlink:href="#DejaVuSans-72" x="1041.357422"/>
    <use xlink:href="#DejaVuSans-65" x="1080.220703"/>
    <use xlink:href="#DejaVuSans-20" x="1141.744141"/>
    <use xlink:href="#DejaVuSans-61" x="1173.53125"/>
    <use xlink:href="#DejaVuSans-6e" x="1234.810547"/>
    <use xlink:href="#DejaVuSans-64" x="1298.189453"/>
    <use xlink:href="#DejaVuSans-20" x="1361.666016"/>
    <use xlink:href="#DejaVuSans-44" x="1393.453125"/>
    <use xlink:href="#DejaVuSans-4f" x="1470.455078"/>
    <use xlink:href="#DejaVuSans-53" x="1549.166016"/>
    <use xlink:href="#DejaVuSans-20" x="1612.642578"/>
    <use xlink:href="#DejaVuSans-6f" x="1644.429688"/>
    <use xlink:href="#DejaVuSans-66" x="1705.611328"/>
    <use xlink:href="#DejaVuSans-20" x="1740.816406"/>
    <use xlink:href="#DejaVuSans-46" x="1772.603516"/>
    <use xlink:href="#DejaVuSans-4c" x="1830.123047"/>
    <use xlink:href="#DejaVuSans-69" x="1885.835938"/>
    <use xlink:href="#DejaVuSans-20" x="1913.619141"/>
    <use xlink:href="#DejaVuSans-28" x="1945.40625"/>
    <use xlink:href="#DejaVuSans-73" x="1984.419922"/>
    <use xlink:href="#DejaVuSans-69" x="2036.519531"/>
    <use xlink:href="#DejaVuSans-6d" x="2064.302734"/>
    <use xlink:href="#DejaVuSans-70" x="2161.714844"/>
    <use xlink:href="#DejaVuSans-6c" x="2225.191406"/>
    <use xlink:href="#DejaVuSans-65" x="2252.974609"/>
    <use xlink:href="#DejaVuSans-5f" x="2314.498047"/>
    <use xlink:href="#DejaVuSans-63" x="2364.498047"/>
    <use xlink:href="#DejaVuSans-75" x="2419.478516"/>
    <use xlink:href="#DejaVuSans-62" x="2482.857422"/>
    <use xlink:href="#DejaVuSans-69" x="2546.333984"/>
    <use xlink:href="#DejaVuSans-63" x="2574.117188"/>
    <use xlink:href="#DejaVuSans-29" x="2629.097656"/>
    <use xlink:href="#DejaVuSans-20" x="2668.111328"/>
    <use xlink:href="#DejaVuSans-77" x="2699.898438"/>
    <use xlink:href="#DejaVuSans-69" x="2781.685547"/>
    <use xlink:href="#DejaVuSans-74" x="2809.46875"/>
    <use xlink:href="#DejaVuSans-68" x="2848.677734"/>
    <use xlink:href="#DejaVuSans-20" x="2912.056641"/>
    <use xlink:href="#DejaVuSans-28" x="2943.84375"/>
    <use xlink:href="#DejaVuSans-32" x="2982.857422"/>
    <use xlink:href="#DejaVuSans-78" x="3046.480469"/>
    <use xlink:href="#DejaVuSans-32" x="3105.660156"/>
    <use xlink:href="#DejaVuSans-78" x="3169.283203"/>
    <use xlink:href="#DejaVuSans-32" x="3228.462891"/>
    <use xlink:href="#DejaVuSans-29" x="3292.085938"/>
    <use xlink:href="#DejaVuSans-20" x="3331.099609"/>
    <use xlink:href="#DejaVuSans-73" x="3362.886719"/>
    <use xlink:href="#DejaVuSans-75" x="3414.986328"/>
    <use xlink:href="#DejaVuSans-70" x="3478.365234"/>
    <use xlink:href="#DejaVuSans-65" x="3541.841797"/>
    <use xlink:href="#DejaVuSans-72" x="3603.365234"/>
    <use xlink:href="#DejaVuSans-63" x="3642.228516"/>
    <use xlink:href="#DejaVuSans-65" x="3697.208984"/>
    <use xlink:href="#DejaVuSans-6c" x="3758.732422"/>
    <use xlink:href="#DejaVuSans-6c" x="3786.515625"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p9b1943e2e1">
   <rect x="48.633125" y="36" width="337.68" height="244.8"/>
  </clipPath>
  <clipPath id="p1cdeb1b793">
   <rect x="391.353125" y="36" width="85.68" height="244.8"/>
  </clipPath>
 </defs>
</svg>
