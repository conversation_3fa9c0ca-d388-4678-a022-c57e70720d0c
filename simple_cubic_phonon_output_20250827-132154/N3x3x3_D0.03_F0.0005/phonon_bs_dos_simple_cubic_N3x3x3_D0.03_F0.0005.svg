<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="484.233125pt" height="320.549375pt" viewBox="0 0 484.233125 320.549375" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-27T13:23:06.776509</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 320.549375 
L 484.233125 320.549375 
L 484.233125 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 48.633125 280.8 
L 386.313125 280.8 
L 386.313125 36 
L 48.633125 36 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="mfbd4dff0e6" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mfbd4dff0e6" x="48.633125" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- G -->
      <g transform="translate(45.53375 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#mfbd4dff0e6" x="93.296968" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- X -->
      <g transform="translate(90.556968 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#mfbd4dff0e6" x="137.960811" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- M -->
      <g transform="translate(134.509561 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#mfbd4dff0e6" x="201.125024" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- G -->
      <g transform="translate(198.025649 293.87875) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-47"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#mfbd4dff0e6" x="278.485069" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- R -->
      <g transform="translate(275.705694 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-52"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#mfbd4dff0e6" x="341.649282" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- X|M -->
      <g transform="translate(334.110532 293.91375) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-7c" d="M 1344 4891 
L 1344 -1509 
L 813 -1509 
L 813 4891 
L 1344 4891 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-7c" x="68.505859"/>
       <use xlink:href="#DejaVuSans-4d" x="102.197266"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#mfbd4dff0e6" x="386.313125" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- R -->
      <g transform="translate(383.53375 293.87875) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-52"/>
      </g>
     </g>
    </g>
    <g id="text_8">
     <!-- Wave vector -->
     <g transform="translate(173.829219 310.437812) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-57"/>
      <use xlink:href="#DejaVuSans-61" x="92.501953"/>
      <use xlink:href="#DejaVuSans-76" x="153.78125"/>
      <use xlink:href="#DejaVuSans-65" x="212.960938"/>
      <use xlink:href="#DejaVuSans-20" x="274.484375"/>
      <use xlink:href="#DejaVuSans-76" x="306.271484"/>
      <use xlink:href="#DejaVuSans-65" x="365.451172"/>
      <use xlink:href="#DejaVuSans-63" x="426.974609"/>
      <use xlink:href="#DejaVuSans-74" x="481.955078"/>
      <use xlink:href="#DejaVuSans-6f" x="521.164062"/>
      <use xlink:href="#DejaVuSans-72" x="582.345703"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_8">
      <defs>
       <path id="mcc30e54a24" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mcc30e54a24" x="48.633125" y="272.680838" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- −10 -->
      <g transform="translate(24.749375 275.720213) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
       <use xlink:href="#DejaVuSans-30" x="147.412109"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_9">
      <g>
       <use xlink:href="#mcc30e54a24" x="48.633125" y="239.371168" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- −5 -->
      <g transform="translate(29.839375 242.410543) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-35" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_10">
      <g>
       <use xlink:href="#mcc30e54a24" x="48.633125" y="206.061497" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0 -->
      <g transform="translate(36.543125 209.100872) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_11">
      <g>
       <use xlink:href="#mcc30e54a24" x="48.633125" y="172.751827" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 5 -->
      <g transform="translate(36.543125 175.791202) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_12">
      <g>
       <use xlink:href="#mcc30e54a24" x="48.633125" y="139.442157" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 10 -->
      <g transform="translate(31.453125 142.481532) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-30" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_13">
      <g>
       <use xlink:href="#mcc30e54a24" x="48.633125" y="106.132486" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 15 -->
      <g transform="translate(31.453125 109.171861) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-35" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_14">
      <g>
       <use xlink:href="#mcc30e54a24" x="48.633125" y="72.822816" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 20 -->
      <g transform="translate(31.453125 75.862191) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
       <use xlink:href="#DejaVuSans-30" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_15">
      <g>
       <use xlink:href="#mcc30e54a24" x="48.633125" y="39.513145" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 25 -->
      <g transform="translate(31.453125 42.55252) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-32"/>
       <use xlink:href="#DejaVuSans-35" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="text_17">
     <!-- Frequency (THz) -->
     <g transform="translate(17.837813 215.462031) rotate(-90) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-72" x="50.269531"/>
      <use xlink:href="#DejaVuSans-65" x="89.132812"/>
      <use xlink:href="#DejaVuSans-71" x="150.65625"/>
      <use xlink:href="#DejaVuSans-75" x="214.132812"/>
      <use xlink:href="#DejaVuSans-65" x="277.511719"/>
      <use xlink:href="#DejaVuSans-6e" x="339.035156"/>
      <use xlink:href="#DejaVuSans-63" x="402.414062"/>
      <use xlink:href="#DejaVuSans-79" x="457.394531"/>
      <use xlink:href="#DejaVuSans-20" x="516.574219"/>
      <use xlink:href="#DejaVuSans-28" x="548.361328"/>
      <use xlink:href="#DejaVuSans-54" x="587.375"/>
      <use xlink:href="#DejaVuSans-48" x="648.458984"/>
      <use xlink:href="#DejaVuSans-7a" x="723.654297"/>
      <use xlink:href="#DejaVuSans-29" x="776.144531"/>
     </g>
    </g>
   </g>
   <g id="line2d_16">
    <path d="M 48.633125 233.893237 
L 52.355112 236.460444 
L 56.077099 235.346661 
L 59.799086 234.826813 
L 63.521073 235.449876 
L 67.24306 236.134628 
L 70.965047 236.815504 
L 74.687033 237.429816 
L 78.40902 237.923193 
L 82.131007 238.255968 
L 85.852994 238.414466 
L 89.574981 238.433709 
L 93.296968 238.415198 
L 97.018955 238.530438 
L 100.740942 238.865986 
L 104.462929 239.392856 
L 108.184916 240.06738 
L 111.906903 240.836797 
L 115.62889 241.645426 
L 119.350877 242.449488 
L 123.072863 243.478203 
L 126.79485 244.466751 
L 130.516837 245.211255 
L 134.238824 245.672454 
L 137.960811 245.828571 
L 141.469934 245.691607 
L 144.979057 245.292106 
L 148.48818 244.678141 
L 151.997303 243.958627 
L 155.506426 243.218617 
L 159.015549 242.443458 
L 162.524672 241.613556 
L 166.033795 240.731261 
L 169.542917 239.810045 
L 173.05204 238.868927 
L 176.561163 237.930779 
L 180.070286 237.021918 
L 183.579409 236.171844 
L 187.088532 235.412518 
L 190.597655 238.67765 
L 194.106778 238.765636 
L 197.615901 236.298679 
L 201.125024 233.893237 
L 204.488504 235.432311 
L 207.851984 237.503947 
L 211.215465 238.118305 
L 214.578945 236.70046 
L 217.942425 232.778498 
L 221.305905 225.125379 
L 224.669385 196.686252 
L 228.032866 181.471407 
L 231.396346 174.797227 
L 234.759826 171.429458 
L 238.123306 168.980655 
L 241.486787 167.288582 
L 244.850267 166.110722 
L 248.213747 165.096001 
L 251.577227 163.830165 
L 254.940708 161.958549 
L 258.304188 159.321454 
L 261.667668 156.004433 
L 265.031148 152.274287 
L 268.394629 148.463395 
L 271.758109 144.880058 
L 275.121589 141.818411 
L 278.485069 140.176427 
L 281.64328 141.551026 
L 284.801491 144.195055 
L 287.959701 147.275542 
L 291.117912 150.625731 
L 294.276122 154.23508 
L 297.434333 158.215703 
L 300.592544 162.684001 
L 303.750754 167.708931 
L 306.908965 173.398738 
L 310.067176 180.012282 
L 313.225386 188.322038 
L 316.383597 210.147035 
L 319.541807 223.626302 
L 322.700018 229.625813 
L 325.858229 233.507743 
L 329.016439 236.075524 
L 332.17465 237.652179 
L 335.332861 238.415887 
L 338.491071 238.534802 
L 341.649282 238.415198 
L 341.649282 245.828571 
L 345.084962 244.80142 
L 348.520642 241.529711 
L 351.956323 235.396129 
L 355.392003 224.195393 
L 358.827683 188.546474 
L 362.263363 174.295497 
L 365.699044 164.395114 
L 369.134724 156.643052 
L 372.570404 150.658642 
L 376.006084 146.753276 
L 379.441765 143.939282 
L 382.877445 141.529769 
L 386.313125 140.176427 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_17">
    <path d="M 48.633125 233.893237 
L 52.355112 234.004956 
L 56.077099 234.32779 
L 59.799086 234.826813 
L 63.521073 235.449876 
L 67.24306 236.134628 
L 70.965047 236.815504 
L 74.687033 237.429816 
L 78.40902 237.923193 
L 82.131007 238.255968 
L 85.852994 238.414466 
L 89.574981 238.433709 
L 93.296968 238.415198 
L 97.018955 238.307818 
L 100.740942 236.540364 
L 104.462929 237.040323 
L 108.184916 238.233597 
L 111.906903 239.569261 
L 115.62889 240.944291 
L 119.350877 242.253665 
L 123.072863 243.137052 
L 126.79485 243.749957 
L 130.516837 244.210618 
L 134.238824 244.497017 
L 137.960811 244.594181 
L 141.469934 244.505294 
L 144.979057 244.230156 
L 148.48818 243.729375 
L 151.997303 242.908331 
L 155.506426 241.701651 
L 159.015549 240.148812 
L 162.524672 238.299139 
L 166.033795 236.184767 
L 169.542917 233.831361 
L 173.05204 231.263628 
L 176.561163 228.506898 
L 180.070286 225.587207 
L 183.579409 226.846736 
L 187.088532 235.266301 
L 190.597655 234.776771 
L 194.106778 234.29572 
L 197.615901 234.470394 
L 201.125024 233.893237 
L 204.488504 235.432311 
L 207.851984 237.503947 
L 211.215465 238.118305 
L 214.578945 236.70046 
L 217.942425 232.778498 
L 221.305905 225.125379 
L 224.669385 196.686252 
L 228.032866 181.471407 
L 231.396346 172.36789 
L 234.759826 166.70673 
L 238.123306 164.876511 
L 241.486787 163.897908 
L 244.850267 162.59356 
L 248.213747 160.775021 
L 251.577227 158.488328 
L 254.940708 155.867538 
L 258.304188 153.064437 
L 261.667668 150.212182 
L 265.031148 147.418059 
L 268.394629 144.774651 
L 271.758109 142.377711 
L 275.121589 140.361847 
L 278.485069 139.262052 
L 281.64328 140.855285 
L 284.801491 143.311857 
L 287.959701 146.131892 
L 291.117912 149.240244 
L 294.276122 152.501448 
L 297.434333 155.708884 
L 300.592544 158.743735 
L 303.750754 161.666243 
L 306.908965 164.645134 
L 310.067176 170.071272 
L 313.225386 177.274321 
L 316.383597 186.161114 
L 319.541807 203.10017 
L 322.700018 224.060729 
L 325.858229 230.469519 
L 329.016439 234.396659 
L 332.17465 236.795026 
L 335.332861 238.051625 
L 338.491071 238.44008 
L 341.649282 238.415198 
L 341.649282 244.594181 
L 345.084962 243.316667 
L 348.520642 239.358425 
L 351.956323 231.943931 
L 355.392003 216.051709 
L 358.827683 182.235622 
L 362.263363 170.094001 
L 365.699044 160.937997 
L 369.134724 153.612628 
L 372.570404 148.935361 
L 376.006084 145.947529 
L 379.441765 143.449687 
L 382.877445 141.01909 
L 386.313125 139.262052 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_18">
    <path d="M 48.633125 233.893237 
L 52.355112 234.004956 
L 56.077099 234.32779 
L 59.799086 226.325801 
L 63.521073 219.926677 
L 67.24306 223.009125 
L 70.965047 225.845969 
L 74.687033 228.397159 
L 78.40902 230.625309 
L 82.131007 232.492327 
L 85.852994 233.950029 
L 89.574981 234.918586 
L 93.296968 235.269386 
L 97.018955 235.480902 
L 100.740942 236.092435 
L 104.462929 232.372167 
L 108.184916 225.011714 
L 111.906903 200.871704 
L 115.62889 185.634989 
L 119.350877 178.377786 
L 123.072863 173.804346 
L 126.79485 171.127386 
L 130.516837 170.120032 
L 134.238824 170.726346 
L 137.960811 171.456263 
L 141.469934 169.992731 
L 144.979057 166.243441 
L 148.48818 162.429005 
L 151.997303 161.559347 
L 155.506426 160.31524 
L 159.015549 158.82453 
L 162.524672 157.367874 
L 166.033795 156.479644 
L 169.542917 159.854639 
L 173.05204 165.021397 
L 176.561163 175.540131 
L 180.070286 192.713485 
L 183.579409 222.530819 
L 187.088532 221.623725 
L 190.597655 229.917999 
L 194.106778 233.688775 
L 197.615901 233.995396 
L 201.125024 233.893237 
L 204.488504 233.591764 
L 207.851984 232.284547 
L 211.215465 229.533864 
L 214.578945 224.916581 
L 217.942425 216.778878 
L 221.305905 193.584433 
L 224.669385 185.097458 
L 228.032866 179.244594 
L 231.396346 172.36789 
L 234.759826 166.70673 
L 238.123306 164.876511 
L 241.486787 163.897908 
L 244.850267 162.59356 
L 248.213747 160.775021 
L 251.577227 158.488328 
L 254.940708 155.867538 
L 258.304188 153.064437 
L 261.667668 150.212182 
L 265.031148 147.418059 
L 268.394629 144.774651 
L 271.758109 142.377711 
L 275.121589 140.361847 
L 278.485069 139.262052 
L 281.64328 139.528725 
L 284.801491 140.325488 
L 287.959701 141.644965 
L 291.117912 143.491641 
L 294.276122 145.998743 
L 297.434333 149.415177 
L 300.592544 153.56457 
L 303.750754 158.369249 
L 306.908965 163.844498 
L 310.067176 167.885502 
L 313.225386 171.588831 
L 316.383597 175.957072 
L 319.541807 181.262572 
L 322.700018 188.110094 
L 325.858229 199.891951 
L 329.016439 221.118726 
L 332.17465 227.540453 
L 335.332861 231.661012 
L 338.491071 234.268557 
L 341.649282 235.269386 
L 341.649282 171.456263 
L 345.084962 170.977899 
L 348.520642 169.60101 
L 351.956323 167.478307 
L 355.392003 164.808188 
L 358.827683 161.789131 
L 362.263363 158.592047 
L 365.699044 155.346664 
L 369.134724 152.116699 
L 372.570404 148.45336 
L 376.006084 145.201099 
L 379.441765 141.961654 
L 382.877445 139.956825 
L 386.313125 139.262052 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_19">
    <path d="M 48.633125 206.061498 
L 52.355112 209.658714 
L 56.077099 213.20295 
L 59.799086 216.642254 
L 63.521073 219.926677 
L 67.24306 223.009125 
L 70.965047 225.845969 
L 74.687033 228.397159 
L 78.40902 230.625309 
L 82.131007 232.492327 
L 85.852994 233.950029 
L 89.574981 234.918586 
L 93.296968 235.269386 
L 97.018955 232.41577 
L 100.740942 224.075585 
L 104.462929 192.317439 
L 108.184916 177.784495 
L 111.906903 168.008977 
L 115.62889 160.749252 
L 119.350877 156.005582 
L 123.072863 154.329034 
L 126.79485 155.975263 
L 130.516837 159.587109 
L 134.238824 162.410172 
L 137.960811 163.217801 
L 141.469934 163.157329 
L 144.979057 162.930096 
L 148.48818 161.584214 
L 151.997303 157.327709 
L 155.506426 154.385671 
L 159.015549 153.199676 
L 162.524672 153.818549 
L 166.033795 156.092314 
L 169.542917 157.741474 
L 173.05204 164.387131 
L 176.561163 171.656847 
L 180.070286 180.157684 
L 183.579409 192.518697 
L 187.088532 219.36358 
L 190.597655 216.110325 
L 194.106778 212.794479 
L 197.615901 209.437975 
L 201.125024 206.061498 
L 204.488504 193.616805 
L 207.851984 184.218926 
L 211.215465 177.259127 
L 214.578945 172.108186 
L 217.942425 168.429597 
L 221.305905 165.981006 
L 224.669385 164.517605 
L 228.032866 163.734456 
L 231.396346 163.142864 
L 234.759826 161.332019 
L 238.123306 156.921273 
L 241.486787 152.343939 
L 244.850267 148.436248 
L 248.213747 145.206751 
L 251.577227 142.56126 
L 254.940708 140.408716 
L 258.304188 138.692148 
L 261.667668 137.398297 
L 265.031148 136.550138 
L 268.394629 136.186081 
L 271.758109 136.331904 
L 275.121589 136.952874 
L 278.485069 137.58 
L 281.64328 137.906833 
L 284.801491 138.88507 
L 287.959701 140.506038 
L 291.117912 142.74026 
L 294.276122 145.42337 
L 297.434333 148.273526 
L 300.592544 151.439003 
L 303.750754 154.984791 
L 306.908965 158.916996 
L 310.067176 163.248356 
L 313.225386 168.018051 
L 316.383597 173.317361 
L 319.541807 179.352172 
L 322.700018 186.666257 
L 325.858229 197.967965 
L 329.016439 220.676604 
L 332.17465 227.406718 
L 335.332861 231.626507 
L 338.491071 234.268436 
L 341.649282 235.269386 
L 341.649282 163.217801 
L 345.084962 162.973236 
L 348.520642 162.253159 
L 351.956323 161.095918 
L 355.392003 159.558039 
L 358.827683 157.705931 
L 362.263363 155.608137 
L 365.699044 153.329571 
L 369.134724 150.928106 
L 372.570404 147.683738 
L 376.006084 143.188277 
L 379.441765 139.989109 
L 382.877445 138.094512 
L 386.313125 137.58 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_20">
    <path d="M 48.633125 206.061497 
L 52.355112 209.658714 
L 56.077099 213.20295 
L 59.799086 216.642254 
L 63.521073 186.660958 
L 67.24306 168.323203 
L 70.965047 154.729134 
L 74.687033 143.593526 
L 78.40902 134.517147 
L 82.131007 127.424227 
L 85.852994 122.325171 
L 89.574981 119.249503 
L 93.296968 118.221309 
L 97.018955 118.327098 
L 100.740942 118.624972 
L 104.462929 119.057225 
L 108.184916 119.531518 
L 111.906903 119.931494 
L 115.62889 120.141825 
L 119.350877 120.095557 
L 123.072863 119.843004 
L 126.79485 119.586836 
L 130.516837 119.432876 
L 134.238824 119.264393 
L 137.960811 119.176754 
L 141.469934 119.417988 
L 144.979057 120.183786 
L 148.48818 121.586961 
L 151.997303 123.777996 
L 155.506426 126.907202 
L 159.015549 131.09066 
L 162.524672 136.368894 
L 166.033795 142.57048 
L 169.542917 148.502131 
L 173.05204 151.393414 
L 176.561163 152.939551 
L 180.070286 155.128062 
L 183.579409 158.522301 
L 187.088532 163.38154 
L 190.597655 169.930128 
L 194.106778 178.522714 
L 197.615901 190.019488 
L 201.125024 206.061497 
L 204.488504 193.616805 
L 207.851984 184.218926 
L 211.215465 177.259127 
L 214.578945 172.108186 
L 217.942425 168.429597 
L 221.305905 165.981006 
L 224.669385 164.517605 
L 228.032866 163.734456 
L 231.396346 163.142864 
L 234.759826 161.332019 
L 238.123306 156.921273 
L 241.486787 152.343939 
L 244.850267 148.436248 
L 248.213747 145.206751 
L 251.577227 142.56126 
L 254.940708 140.408716 
L 258.304188 138.692148 
L 261.667668 137.398297 
L 265.031148 136.550138 
L 268.394629 136.186081 
L 271.758109 136.331904 
L 275.121589 136.952874 
L 278.485069 137.58 
L 281.64328 136.220226 
L 284.801491 134.544203 
L 287.959701 133.068188 
L 291.117912 131.763936 
L 294.276122 130.539578 
L 297.434333 129.302903 
L 300.592544 128.044771 
L 303.750754 126.820911 
L 306.908965 125.66008 
L 310.067176 124.560587 
L 313.225386 123.517643 
L 316.383597 122.53254 
L 319.541807 121.613384 
L 322.700018 120.773528 
L 325.858229 120.029122 
L 329.016439 119.396497 
L 332.17465 118.890025 
L 335.332861 118.52085 
L 338.491071 118.296531 
L 341.649282 118.221309 
L 341.649282 119.176754 
L 345.084962 119.317262 
L 348.520642 119.736266 
L 351.956323 120.42633 
L 355.392003 121.375475 
L 358.827683 122.567776 
L 362.263363 123.984146 
L 365.699044 125.603228 
L 369.134724 127.4023 
L 372.570404 129.357972 
L 376.006084 131.446164 
L 379.441765 133.63935 
L 382.877445 135.884956 
L 386.313125 137.58 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_21">
    <path d="M 48.633125 206.061497 
L 52.355112 181.46355 
L 56.077099 162.768956 
L 59.799086 146.462891 
L 63.521073 131.670832 
L 67.24306 118.250666 
L 70.965047 106.260204 
L 74.687033 95.818806 
L 78.40902 87.058668 
L 82.131007 80.10338 
L 85.852994 75.056826 
L 89.574981 71.996833 
L 93.296968 70.971429 
L 97.018955 71.852044 
L 100.740942 74.460199 
L 104.462929 78.690586 
L 108.184916 84.351281 
L 111.906903 91.129754 
L 115.62889 98.522712 
L 119.350877 105.713564 
L 123.072863 111.495373 
L 126.79485 114.749865 
L 130.516837 115.686227 
L 134.238824 115.664779 
L 137.960811 115.579352 
L 141.469934 115.513502 
L 144.979057 115.28923 
L 148.48818 114.836652 
L 151.997303 114.087588 
L 155.506426 113.075457 
L 159.015549 112.045733 
L 162.524672 111.452526 
L 166.033795 111.815233 
L 169.542917 113.57004 
L 173.05204 117.006062 
L 176.561163 122.264897 
L 180.070286 129.362375 
L 183.579409 138.21451 
L 187.088532 148.668055 
L 190.597655 160.550458 
L 194.106778 173.783433 
L 197.615901 188.67891 
L 201.125024 206.061497 
L 204.488504 192.285415 
L 207.851984 179.275731 
L 211.215465 167.218257 
L 214.578945 156.279358 
L 217.942425 146.673838 
L 221.305905 138.586744 
L 224.669385 132.131674 
L 228.032866 127.331915 
L 231.396346 124.112895 
L 234.759826 122.304417 
L 238.123306 121.655857 
L 241.486787 121.868219 
L 244.850267 122.641866 
L 248.213747 123.72721 
L 251.577227 124.957037 
L 254.940708 126.247382 
L 258.304188 127.574778 
L 261.667668 128.94948 
L 265.031148 130.398208 
L 268.394629 131.957139 
L 271.758109 133.663077 
L 275.121589 135.488417 
L 278.485069 136.699845 
L 281.64328 135.714384 
L 284.801491 134.104987 
L 287.959701 132.533328 
L 291.117912 131.023541 
L 294.276122 129.502584 
L 297.434333 127.830829 
L 300.592544 125.753225 
L 303.750754 122.967279 
L 306.908965 119.282952 
L 310.067176 114.682588 
L 313.225386 109.304861 
L 316.383597 103.400218 
L 319.541807 97.27561 
L 322.700018 91.248194 
L 325.858229 85.615602 
L 329.016439 80.639915 
L 332.17465 76.539936 
L 335.332861 73.487667 
L 338.491071 71.606698 
L 341.649282 70.971429 
L 341.649282 115.579352 
L 345.084962 115.78215 
L 348.520642 116.383776 
L 351.956323 117.36419 
L 355.392003 118.690904 
L 358.827683 120.320464 
L 362.263363 122.200726 
L 365.699044 124.27405 
L 369.134724 126.481397 
L 372.570404 128.766817 
L 376.006084 131.080387 
L 379.441765 133.371646 
L 382.877445 135.521133 
L 386.313125 136.699845 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_22">
    <path d="M 48.633125 280.8 
L 48.633125 36 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_23">
    <path d="M 93.296968 280.8 
L 93.296968 36 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_24">
    <path d="M 137.960811 280.8 
L 137.960811 36 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_25">
    <path d="M 201.125024 280.8 
L 201.125024 36 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_26">
    <path d="M 278.485069 280.8 
L 278.485069 36 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_27">
    <path d="M 341.649282 280.8 
L 341.649282 36 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_28">
    <path d="M 341.649282 280.8 
L 341.649282 36 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_29">
    <path d="M 386.313125 280.8 
L 386.313125 36 
" clip-path="url(#p9f19f70abf)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="patch_3">
    <path d="M 48.633125 280.8 
L 48.633125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 386.313125 280.8 
L 386.313125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 48.633125 280.8 
L 386.313125 280.8 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 48.633125 36 
L 386.313125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_7">
    <path d="M 391.353125 280.8 
L 477.033125 280.8 
L 477.033125 36 
L 391.353125 36 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="md8f5284921" d="M 395.248622 -114.487878 
L 395.248622 -74.370519 
L 398.421574 -75.252127 
L 404.931261 -76.133734 
L 409.441404 -77.015342 
L 417.642386 -77.89695 
L 420.024476 -78.778558 
L 429.568704 -79.660166 
L 437.527082 -80.541774 
L 451.87735 -81.423381 
L 473.13858 -82.304989 
L 470.722581 -83.186597 
L 451.739759 -84.068205 
L 450.422747 -84.949813 
L 443.551426 -85.831421 
L 439.757323 -86.713028 
L 434.357484 -87.594636 
L 430.479775 -88.476244 
L 423.922926 -89.357852 
L 425.182028 -90.23946 
L 424.845843 -91.121067 
L 420.041272 -92.002675 
L 423.289207 -92.884283 
L 418.733673 -93.765891 
L 421.344355 -94.647499 
L 422.799587 -95.529107 
L 415.151864 -96.410714 
L 415.43208 -97.292322 
L 413.892832 -98.17393 
L 406.983974 -99.055538 
L 409.367735 -99.937146 
L 411.04128 -100.818754 
L 408.535905 -101.700361 
L 408.618067 -102.581969 
L 407.550291 -103.463577 
L 405.931867 -104.345185 
L 405.504769 -105.226793 
L 404.17778 -106.108401 
L 404.839196 -106.990008 
L 401.599927 -107.871616 
L 401.197226 -108.753224 
L 401.127058 -109.634832 
L 400.739926 -110.51644 
L 398.64416 -111.398047 
L 398.181373 -112.279655 
L 395.944541 -113.161263 
L 396.146887 -114.042871 
L 396.298648 -114.924479 
L 397.008278 -115.806087 
L 398.785814 -116.687694 
L 398.607313 -117.569302 
L 399.766593 -118.45091 
L 401.659875 -119.332518 
L 401.230311 -120.214126 
L 399.995964 -121.095734 
L 402.292995 -121.977341 
L 403.494381 -122.858949 
L 402.799179 -123.740557 
L 403.36037 -124.622165 
L 404.430559 -125.503773 
L 404.354489 -126.38538 
L 406.655356 -127.266988 
L 406.8184 -128.148596 
L 406.75248 -129.030204 
L 412.429899 -129.911812 
L 412.913072 -130.79342 
L 414.229589 -131.675027 
L 412.477377 -132.556635 
L 411.053015 -133.438243 
L 416.040506 -134.319851 
L 414.434308 -135.201459 
L 417.303807 -136.083067 
L 411.746882 -136.964674 
L 408.986533 -137.846282 
L 415.238269 -138.72789 
L 419.150904 -139.609498 
L 421.566244 -140.491106 
L 421.264525 -141.372714 
L 412.697141 -142.254321 
L 420.034476 -143.135929 
L 426.813448 -144.017537 
L 422.277783 -144.899145 
L 422.389583 -145.780753 
L 425.667318 -146.66236 
L 426.053194 -147.543968 
L 422.57528 -148.425576 
L 424.829628 -149.307184 
L 433.613489 -150.188792 
L 432.477259 -151.0704 
L 437.297362 -151.952007 
L 436.033849 -152.833615 
L 425.417354 -153.715223 
L 436.011822 -154.596831 
L 441.678158 -155.478439 
L 434.06142 -156.360047 
L 439.881947 -157.241654 
L 437.273359 -158.123262 
L 446.135611 -159.00487 
L 446.191158 -159.886478 
L 443.274086 -160.768086 
L 444.403306 -161.649693 
L 448.922179 -162.531301 
L 440.85011 -163.412909 
L 440.34378 -164.294517 
L 450.598156 -165.176125 
L 467.277144 -166.057733 
L 452.462688 -166.93934 
L 424.572307 -167.820948 
L 419.795017 -168.702556 
L 423.643221 -169.584164 
L 413.46895 -170.465772 
L 411.945755 -171.34738 
L 410.419849 -172.228987 
L 407.247861 -173.110595 
L 406.298288 -173.992203 
L 411.390854 -174.873811 
L 420.606201 -175.755419 
L 404.163367 -176.637027 
L 407.185201 -177.518634 
L 407.851568 -178.400242 
L 403.44101 -179.28185 
L 406.387625 -180.163458 
L 416.864685 -181.045066 
L 403.298888 -181.926673 
L 403.002063 -182.808281 
L 403.871307 -183.689889 
L 405.185963 -184.571497 
L 418.602251 -185.453105 
L 415.833891 -186.334713 
L 408.279935 -187.21632 
L 411.485266 -188.097928 
L 415.06952 -188.979536 
L 429.382181 -189.861144 
L 419.40679 -190.742752 
L 415.044159 -191.62436 
L 426.538432 -192.505967 
L 438.354083 -193.387575 
L 431.094425 -194.269183 
L 431.76297 -195.150791 
L 436.164549 -196.032399 
L 436.221619 -196.914007 
L 449.171414 -197.795614 
L 443.072394 -198.677222 
L 455.675597 -199.55883 
L 445.345261 -200.440438 
L 431.901693 -201.322046 
L 418.152048 -202.203653 
L 416.474705 -203.085261 
L 419.451395 -203.966869 
L 426.461846 -204.848477 
L 417.155803 -205.730085 
L 417.847738 -206.611693 
L 415.808646 -207.4933 
L 419.037848 -208.374908 
L 410.19611 -209.256516 
L 412.451438 -210.138124 
L 409.573771 -211.019732 
L 405.279586 -211.90134 
L 406.743721 -212.782947 
L 407.122263 -213.664555 
L 405.203027 -214.546163 
L 405.236724 -215.427771 
L 404.096351 -216.309379 
L 407.527213 -217.190986 
L 402.325454 -218.072594 
L 401.378921 -218.954202 
L 405.956623 -219.83581 
L 401.829288 -220.717418 
L 401.983036 -221.599026 
L 402.044439 -222.480633 
L 401.677455 -223.362241 
L 403.215757 -224.243849 
L 399.215373 -225.125457 
L 402.14162 -226.007065 
L 399.493567 -226.888673 
L 402.232456 -227.77028 
L 401.176744 -228.651888 
L 398.923305 -229.533496 
L 399.388592 -230.415104 
L 400.015042 -231.296712 
L 400.475371 -232.17832 
L 402.643134 -233.059927 
L 397.025148 -233.941535 
L 400.933256 -234.823143 
L 399.166019 -235.704751 
L 399.27773 -236.586359 
L 398.12191 -237.467966 
L 398.592266 -238.349574 
L 397.755377 -239.231182 
L 397.52442 -240.11279 
L 397.66896 -240.994398 
L 397.880812 -241.876006 
L 397.494908 -242.757613 
L 397.483689 -243.639221 
L 396.991726 -244.520829 
L 397.032015 -245.402437 
L 396.342427 -246.284045 
L 396.484103 -247.165653 
L 396.760909 -248.04726 
L 395.711287 -248.928868 
L 395.24767 -249.810476 
L 395.24767 -114.487878 
L 395.24767 -114.487878 
L 395.711287 -114.487878 
L 396.760909 -114.487878 
L 396.484103 -114.487878 
L 396.342427 -114.487878 
L 397.032015 -114.487878 
L 396.991726 -114.487878 
L 397.483689 -114.487878 
L 397.494908 -114.487878 
L 397.880812 -114.487878 
L 397.66896 -114.487878 
L 397.52442 -114.487878 
L 397.755377 -114.487878 
L 398.592266 -114.487878 
L 398.12191 -114.487878 
L 399.27773 -114.487878 
L 399.166019 -114.487878 
L 400.933256 -114.487878 
L 397.025148 -114.487878 
L 402.643134 -114.487878 
L 400.475371 -114.487878 
L 400.015042 -114.487878 
L 399.388592 -114.487878 
L 398.923305 -114.487878 
L 401.176744 -114.487878 
L 402.232456 -114.487878 
L 399.493567 -114.487878 
L 402.14162 -114.487878 
L 399.215373 -114.487878 
L 403.215757 -114.487878 
L 401.677455 -114.487878 
L 402.044439 -114.487878 
L 401.983036 -114.487878 
L 401.829288 -114.487878 
L 405.956623 -114.487878 
L 401.378921 -114.487878 
L 402.325454 -114.487878 
L 407.527213 -114.487878 
L 404.096351 -114.487878 
L 405.236724 -114.487878 
L 405.203027 -114.487878 
L 407.122263 -114.487878 
L 406.743721 -114.487878 
L 405.279586 -114.487878 
L 409.573771 -114.487878 
L 412.451438 -114.487878 
L 410.19611 -114.487878 
L 419.037848 -114.487878 
L 415.808646 -114.487878 
L 417.847738 -114.487878 
L 417.155803 -114.487878 
L 426.461846 -114.487878 
L 419.451395 -114.487878 
L 416.474705 -114.487878 
L 418.152048 -114.487878 
L 431.901693 -114.487878 
L 445.345261 -114.487878 
L 455.675597 -114.487878 
L 443.072394 -114.487878 
L 449.171414 -114.487878 
L 436.221619 -114.487878 
L 436.164549 -114.487878 
L 431.76297 -114.487878 
L 431.094425 -114.487878 
L 438.354083 -114.487878 
L 426.538432 -114.487878 
L 415.044159 -114.487878 
L 419.40679 -114.487878 
L 429.382181 -114.487878 
L 415.06952 -114.487878 
L 411.485266 -114.487878 
L 408.279935 -114.487878 
L 415.833891 -114.487878 
L 418.602251 -114.487878 
L 405.185963 -114.487878 
L 403.871307 -114.487878 
L 403.002063 -114.487878 
L 403.298888 -114.487878 
L 416.864685 -114.487878 
L 406.387625 -114.487878 
L 403.44101 -114.487878 
L 407.851568 -114.487878 
L 407.185201 -114.487878 
L 404.163367 -114.487878 
L 420.606201 -114.487878 
L 411.390854 -114.487878 
L 406.298288 -114.487878 
L 407.247861 -114.487878 
L 410.419849 -114.487878 
L 411.945755 -114.487878 
L 413.46895 -114.487878 
L 423.643221 -114.487878 
L 419.795017 -114.487878 
L 424.572307 -114.487878 
L 452.462688 -114.487878 
L 467.277144 -114.487878 
L 450.598156 -114.487878 
L 440.34378 -114.487878 
L 440.85011 -114.487878 
L 448.922179 -114.487878 
L 444.403306 -114.487878 
L 443.274086 -114.487878 
L 446.191158 -114.487878 
L 446.135611 -114.487878 
L 437.273359 -114.487878 
L 439.881947 -114.487878 
L 434.06142 -114.487878 
L 441.678158 -114.487878 
L 436.011822 -114.487878 
L 425.417354 -114.487878 
L 436.033849 -114.487878 
L 437.297362 -114.487878 
L 432.477259 -114.487878 
L 433.613489 -114.487878 
L 424.829628 -114.487878 
L 422.57528 -114.487878 
L 426.053194 -114.487878 
L 425.667318 -114.487878 
L 422.389583 -114.487878 
L 422.277783 -114.487878 
L 426.813448 -114.487878 
L 420.034476 -114.487878 
L 412.697141 -114.487878 
L 421.264525 -114.487878 
L 421.566244 -114.487878 
L 419.150904 -114.487878 
L 415.238269 -114.487878 
L 408.986533 -114.487878 
L 411.746882 -114.487878 
L 417.303807 -114.487878 
L 414.434308 -114.487878 
L 416.040506 -114.487878 
L 411.053015 -114.487878 
L 412.477377 -114.487878 
L 414.229589 -114.487878 
L 412.913072 -114.487878 
L 412.429899 -114.487878 
L 406.75248 -114.487878 
L 406.8184 -114.487878 
L 406.655356 -114.487878 
L 404.354489 -114.487878 
L 404.430559 -114.487878 
L 403.36037 -114.487878 
L 402.799179 -114.487878 
L 403.494381 -114.487878 
L 402.292995 -114.487878 
L 399.995964 -114.487878 
L 401.230311 -114.487878 
L 401.659875 -114.487878 
L 399.766593 -114.487878 
L 398.607313 -114.487878 
L 398.785814 -114.487878 
L 397.008278 -114.487878 
L 396.298648 -114.487878 
L 396.146887 -114.487878 
L 395.944541 -114.487878 
L 398.181373 -114.487878 
L 398.64416 -114.487878 
L 400.739926 -114.487878 
L 401.127058 -114.487878 
L 401.197226 -114.487878 
L 401.599927 -114.487878 
L 404.839196 -114.487878 
L 404.17778 -114.487878 
L 405.504769 -114.487878 
L 405.931867 -114.487878 
L 407.550291 -114.487878 
L 408.618067 -114.487878 
L 408.535905 -114.487878 
L 411.04128 -114.487878 
L 409.367735 -114.487878 
L 406.983974 -114.487878 
L 413.892832 -114.487878 
L 415.43208 -114.487878 
L 415.151864 -114.487878 
L 422.799587 -114.487878 
L 421.344355 -114.487878 
L 418.733673 -114.487878 
L 423.289207 -114.487878 
L 420.041272 -114.487878 
L 424.845843 -114.487878 
L 425.182028 -114.487878 
L 423.922926 -114.487878 
L 430.479775 -114.487878 
L 434.357484 -114.487878 
L 439.757323 -114.487878 
L 443.551426 -114.487878 
L 450.422747 -114.487878 
L 451.739759 -114.487878 
L 470.722581 -114.487878 
L 473.13858 -114.487878 
L 451.87735 -114.487878 
L 437.527082 -114.487878 
L 429.568704 -114.487878 
L 420.024476 -114.487878 
L 417.642386 -114.487878 
L 409.441404 -114.487878 
L 404.931261 -114.487878 
L 398.421574 -114.487878 
L 395.248622 -114.487878 
z
" style="stroke: #000000"/>
    </defs>
    <g clip-path="url(#pc787682600)">
     <use xlink:href="#md8f5284921" x="0" y="320.549375" style="fill: #808080; stroke: #000000"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="text_18">
     <!-- DOS -->
     <g transform="translate(418.848906 295.437812) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-44"/>
      <use xlink:href="#DejaVuSans-4f" x="77.001953"/>
      <use xlink:href="#DejaVuSans-53" x="155.712891"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4"/>
   <g id="patch_8">
    <path d="M 391.353125 280.8 
L 391.353125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 477.033125 280.8 
L 477.033125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 391.353125 280.8 
L 477.033125 280.8 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_11">
    <path d="M 391.353125 36 
L 477.033125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
  <g id="text_19">
   <!-- Phonon band structure and DOS of FLi (simple_cubic) with (3x3x3) supercell -->
   <g transform="translate(11.29625 16.318125) scale(0.12 -0.12)">
    <defs>
     <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#DejaVuSans-50"/>
    <use xlink:href="#DejaVuSans-68" x="60.302734"/>
    <use xlink:href="#DejaVuSans-6f" x="123.681641"/>
    <use xlink:href="#DejaVuSans-6e" x="184.863281"/>
    <use xlink:href="#DejaVuSans-6f" x="248.242188"/>
    <use xlink:href="#DejaVuSans-6e" x="309.423828"/>
    <use xlink:href="#DejaVuSans-20" x="372.802734"/>
    <use xlink:href="#DejaVuSans-62" x="404.589844"/>
    <use xlink:href="#DejaVuSans-61" x="468.066406"/>
    <use xlink:href="#DejaVuSans-6e" x="529.345703"/>
    <use xlink:href="#DejaVuSans-64" x="592.724609"/>
    <use xlink:href="#DejaVuSans-20" x="656.201172"/>
    <use xlink:href="#DejaVuSans-73" x="687.988281"/>
    <use xlink:href="#DejaVuSans-74" x="740.087891"/>
    <use xlink:href="#DejaVuSans-72" x="779.296875"/>
    <use xlink:href="#DejaVuSans-75" x="820.410156"/>
    <use xlink:href="#DejaVuSans-63" x="883.789062"/>
    <use xlink:href="#DejaVuSans-74" x="938.769531"/>
    <use xlink:href="#DejaVuSans-75" x="977.978516"/>
    <use xlink:href="#DejaVuSans-72" x="1041.357422"/>
    <use xlink:href="#DejaVuSans-65" x="1080.220703"/>
    <use xlink:href="#DejaVuSans-20" x="1141.744141"/>
    <use xlink:href="#DejaVuSans-61" x="1173.53125"/>
    <use xlink:href="#DejaVuSans-6e" x="1234.810547"/>
    <use xlink:href="#DejaVuSans-64" x="1298.189453"/>
    <use xlink:href="#DejaVuSans-20" x="1361.666016"/>
    <use xlink:href="#DejaVuSans-44" x="1393.453125"/>
    <use xlink:href="#DejaVuSans-4f" x="1470.455078"/>
    <use xlink:href="#DejaVuSans-53" x="1549.166016"/>
    <use xlink:href="#DejaVuSans-20" x="1612.642578"/>
    <use xlink:href="#DejaVuSans-6f" x="1644.429688"/>
    <use xlink:href="#DejaVuSans-66" x="1705.611328"/>
    <use xlink:href="#DejaVuSans-20" x="1740.816406"/>
    <use xlink:href="#DejaVuSans-46" x="1772.603516"/>
    <use xlink:href="#DejaVuSans-4c" x="1830.123047"/>
    <use xlink:href="#DejaVuSans-69" x="1885.835938"/>
    <use xlink:href="#DejaVuSans-20" x="1913.619141"/>
    <use xlink:href="#DejaVuSans-28" x="1945.40625"/>
    <use xlink:href="#DejaVuSans-73" x="1984.419922"/>
    <use xlink:href="#DejaVuSans-69" x="2036.519531"/>
    <use xlink:href="#DejaVuSans-6d" x="2064.302734"/>
    <use xlink:href="#DejaVuSans-70" x="2161.714844"/>
    <use xlink:href="#DejaVuSans-6c" x="2225.191406"/>
    <use xlink:href="#DejaVuSans-65" x="2252.974609"/>
    <use xlink:href="#DejaVuSans-5f" x="2314.498047"/>
    <use xlink:href="#DejaVuSans-63" x="2364.498047"/>
    <use xlink:href="#DejaVuSans-75" x="2419.478516"/>
    <use xlink:href="#DejaVuSans-62" x="2482.857422"/>
    <use xlink:href="#DejaVuSans-69" x="2546.333984"/>
    <use xlink:href="#DejaVuSans-63" x="2574.117188"/>
    <use xlink:href="#DejaVuSans-29" x="2629.097656"/>
    <use xlink:href="#DejaVuSans-20" x="2668.111328"/>
    <use xlink:href="#DejaVuSans-77" x="2699.898438"/>
    <use xlink:href="#DejaVuSans-69" x="2781.685547"/>
    <use xlink:href="#DejaVuSans-74" x="2809.46875"/>
    <use xlink:href="#DejaVuSans-68" x="2848.677734"/>
    <use xlink:href="#DejaVuSans-20" x="2912.056641"/>
    <use xlink:href="#DejaVuSans-28" x="2943.84375"/>
    <use xlink:href="#DejaVuSans-33" x="2982.857422"/>
    <use xlink:href="#DejaVuSans-78" x="3046.480469"/>
    <use xlink:href="#DejaVuSans-33" x="3105.660156"/>
    <use xlink:href="#DejaVuSans-78" x="3169.283203"/>
    <use xlink:href="#DejaVuSans-33" x="3228.462891"/>
    <use xlink:href="#DejaVuSans-29" x="3292.085938"/>
    <use xlink:href="#DejaVuSans-20" x="3331.099609"/>
    <use xlink:href="#DejaVuSans-73" x="3362.886719"/>
    <use xlink:href="#DejaVuSans-75" x="3414.986328"/>
    <use xlink:href="#DejaVuSans-70" x="3478.365234"/>
    <use xlink:href="#DejaVuSans-65" x="3541.841797"/>
    <use xlink:href="#DejaVuSans-72" x="3603.365234"/>
    <use xlink:href="#DejaVuSans-63" x="3642.228516"/>
    <use xlink:href="#DejaVuSans-65" x="3697.208984"/>
    <use xlink:href="#DejaVuSans-6c" x="3758.732422"/>
    <use xlink:href="#DejaVuSans-6c" x="3786.515625"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p9f19f70abf">
   <rect x="48.633125" y="36" width="337.68" height="244.8"/>
  </clipPath>
  <clipPath id="pc787682600">
   <rect x="391.353125" y="36" width="85.68" height="244.8"/>
  </clipPath>
 </defs>
</svg>
