#!/usr/bin/env python3
"""
Test script to verify both fixes are working correctly.
"""

import sys
import os
import tempfile
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from vibroml.utils.genetic_algorithm import GeneticAlgorithm
from vibroml.utils.relaxation_utils import MinimumIterationOptimizer, EnergyVolumeStopper

def test_mutation_tracking_fix():
    """Test that mutation tracking data is properly generated and accessible."""
    print("Testing mutation tracking fix...")
    
    # Create GA with tracked data
    tracked_k_points_data = {
        'soft_modes': [
            {
                'label': 'X',
                'coordinate': [0.5, 0.0, 0.0],
                'frequency': -2.5,
                'band_index': 0,
                'kpoint_index_in_path': 5
            }
        ],
        'highest_freq_modes': [],
        'lowest_freq_modes': []
    }
    
    ga = GeneticAlgorithm(
        population_size=10,
        mutation_rate=0.5,  # High mutation rate to ensure some replacements
        displacement_scale_bounds=(0.1, 2.0),
        ratio_mode2_to_mode1_bounds=(-1.0, 1.0),
        cell_scale_bounds=(-0.1, 0.1),
        cell_angle_bounds=(-5.0, 5.0),
        supercell_variants=[(2, 2, 2)],
        tracked_k_points_data=tracked_k_points_data
    )
    
    # Initialize population
    ga.initialize_population()
    
    # Simulate some mutation data for testing
    ga.last_generation_mutation_data = [
        {'mode_replaced': True, 'selected_mode': {'label': 'X', 'frequency': -2.5, 'band_index': 0}},
        {'mode_replaced': False, 'selected_mode': None},
        {'mode_replaced': True, 'selected_mode': {'label': 'X', 'frequency': -2.5, 'band_index': 0}},
    ]
    
    # Test mutation summary generation
    summary = ga.get_mutation_summary()
    
    print(f"  ✓ Mutation summary generated successfully")
    print(f"    - Total individuals: {summary['total_individuals']}")
    print(f"    - Mode replacements: {summary['mode_replacements']}")
    print(f"    - Replacement rate: {summary['replacement_rate']:.2%}")
    print(f"    - Selected modes: {len(summary['selected_modes'])}")
    
    # Test that the summary has expected structure
    assert 'total_individuals' in summary
    assert 'mode_replacements' in summary
    assert 'replacement_rate' in summary
    assert 'selected_modes' in summary
    
    print("  ✓ Mutation tracking fix verified!")
    return True

def test_minimum_iteration_fix():
    """Test that minimum iteration enforcement is working."""
    print("Testing minimum iteration fix...")
    
    # Test MinimumIterationOptimizer class
    class MockOptimizer:
        def __init__(self):
            self.fmax = 0.05
            self.converged_calls = 0
            
        def converged(self, forces=None):
            self.converged_calls += 1
            # Always return True to test that minimum iterations prevent early stopping
            return True
            
        def run(self, fmax=0.05, steps=None):
            self.fmax = fmax
            return "mock_run_complete"
            
        def attach(self, callback):
            pass
    
    mock_opt = MockOptimizer()
    min_iter_opt = MinimumIterationOptimizer(mock_opt, min_iterations=5)
    
    # Test that convergence is blocked for first few iterations
    for i in range(1, 8):
        result = min_iter_opt._custom_converged()
        if i < 5:
            assert result == False, f"Should not converge at iteration {i}"
            print(f"    ✓ Iteration {i}: Correctly blocked convergence")
        else:
            assert result == True, f"Should converge at iteration {i}"
            print(f"    ✓ Iteration {i}: Correctly allowed convergence")
    
    # Test EnergyVolumeStopper with minimum iterations
    energy_stopper = EnergyVolumeStopper(
        mock_opt,
        energy_increase_threshold=0.5,
        energy_decrease_threshold=-5.0,
        volume_threshold=2.5,
        max_steps=1000,
        min_iterations=5
    )
    
    assert energy_stopper.min_iterations == 5
    print(f"    ✓ EnergyVolumeStopper correctly configured with min_iterations={energy_stopper.min_iterations}")
    
    print("  ✓ Minimum iteration fix verified!")
    return True

def test_population_summary_integration():
    """Test that mutation summary would be properly integrated into population summaries."""
    print("Testing population summary integration...")
    
    # Create a mock mutation summary
    mutation_summary = {
        'total_individuals': 10,
        'mode_replacements': 3,
        'replacement_rate': 0.3,
        'selected_modes': [
            {'label': 'X', 'frequency': -2.5, 'band_index': 0},
            {'label': 'M', 'frequency': -1.8, 'band_index': 1},
            {'label': 'X', 'frequency': -2.5, 'band_index': 0}
        ]
    }
    
    # Test summary formatting (simulate what would be written to file)
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
        # Simulate the summary writing code
        if mutation_summary:
            f.write(f"--- GA Mutation Tracking Summary ---\n")
            f.write(f"Total Individuals: {mutation_summary['total_individuals']}\n")
            f.write(f"Mode Replacements: {mutation_summary['mode_replacements']}\n")
            f.write(f"Replacement Rate: {mutation_summary['replacement_rate']:.2%}\n")
            if mutation_summary['selected_modes']:
                f.write(f"Selected Modes for Replacement:\n")
                for i, mode in enumerate(mutation_summary['selected_modes'][:5]):  # Show first 5
                    f.write(f"  {i+1}. {mode['label']} (freq: {mode['frequency']:.4f}, band: {mode['band_index']})\n")
                if len(mutation_summary['selected_modes']) > 5:
                    f.write(f"  ... and {len(mutation_summary['selected_modes']) - 5} more\n")
            else:
                f.write(f"No modes were selected for replacement in this generation.\n")
            f.write(f"\n")
        
        temp_file = f.name
    
    # Read back and verify content
    with open(temp_file, 'r') as f:
        content = f.read()
    
    os.unlink(temp_file)
    
    # Verify expected content is present
    assert "GA Mutation Tracking Summary" in content
    assert "Total Individuals: 10" in content
    assert "Mode Replacements: 3" in content
    assert "Replacement Rate: 30.00%" in content
    assert "Selected Modes for Replacement:" in content
    assert "1. X (freq: -2.5000, band: 0)" in content
    
    print("  ✓ Population summary integration verified!")
    print(f"    - Summary content properly formatted")
    print(f"    - All expected fields present")
    return True

def main():
    """Run all tests."""
    print("Running comprehensive tests for both fixes...\n")
    
    try:
        test_mutation_tracking_fix()
        print()
        test_minimum_iteration_fix()
        print()
        test_population_summary_integration()
        print()
        print("🎉 All tests passed! Both fixes are working correctly.")
        return True
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
