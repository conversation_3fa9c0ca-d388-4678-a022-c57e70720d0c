#!/usr/bin/env python3
"""
Analyze HfO2 structure using pymatgen to reduce to primitive cell
and verify space group information.
"""

import os
import sys
from pymatgen.core import Structure
from pymatgen.symmetry.analyzer import SpacegroupAnalyzer
from pymatgen.io.cif import CifWriter
import numpy as np

def analyze_structure(cif_file_path):
    """
    Analyze the HfO2 structure and reduce to primitive cell.
    
    Args:
        cif_file_path (str): Path to the input CIF file
    """
    print("="*60)
    print("HfO2 Structure Analysis using PyMatGen")
    print("="*60)
    
    # Load the structure from CIF file
    print(f"\n1. Loading structure from: {cif_file_path}")
    try:
        structure = Structure.from_file(cif_file_path)
        print(f"   ✓ Successfully loaded structure")
    except Exception as e:
        print(f"   ✗ Error loading structure: {e}")
        return None
    
    # Analyze original structure
    print(f"\n2. Original Structure Analysis:")
    print(f"   Formula: {structure.composition.reduced_formula}")
    print(f"   Number of atoms: {len(structure)}")
    print(f"   Lattice parameters:")
    print(f"     a = {structure.lattice.a:.6f} Å")
    print(f"     b = {structure.lattice.b:.6f} Å") 
    print(f"     c = {structure.lattice.c:.6f} Å")
    print(f"     α = {structure.lattice.alpha:.2f}°")
    print(f"     β = {structure.lattice.beta:.2f}°")
    print(f"     γ = {structure.lattice.gamma:.2f}°")
    print(f"   Volume: {structure.lattice.volume:.2f} Å³")
    
    # Analyze space group of original structure
    print(f"\n3. Space Group Analysis (Original):")
    sga_original = SpacegroupAnalyzer(structure)
    print(f"   Space group symbol: {sga_original.get_space_group_symbol()}")
    print(f"   Space group number: {sga_original.get_space_group_number()}")
    print(f"   Crystal system: {sga_original.get_crystal_system()}")
    print(f"   Point group: {sga_original.get_point_group_symbol()}")
    
    # Get primitive structure
    print(f"\n4. Reducing to Primitive Cell:")
    try:
        primitive_structure = sga_original.get_primitive_standard_structure()
        print(f"   ✓ Successfully obtained primitive structure")
    except Exception as e:
        print(f"   ✗ Error getting primitive structure: {e}")
        return None
    
    # Analyze primitive structure
    print(f"\n5. Primitive Structure Analysis:")
    print(f"   Formula: {primitive_structure.composition.reduced_formula}")
    print(f"   Number of atoms: {len(primitive_structure)}")
    print(f"   Lattice parameters:")
    print(f"     a = {primitive_structure.lattice.a:.6f} Å")
    print(f"     b = {primitive_structure.lattice.b:.6f} Å")
    print(f"     c = {primitive_structure.lattice.c:.6f} Å")
    print(f"     α = {primitive_structure.lattice.alpha:.2f}°")
    print(f"     β = {primitive_structure.lattice.beta:.2f}°")
    print(f"     γ = {primitive_structure.lattice.gamma:.2f}°")
    print(f"   Volume: {primitive_structure.lattice.volume:.2f} Å³")
    
    # Analyze space group of primitive structure
    print(f"\n6. Space Group Analysis (Primitive):")
    sga_primitive = SpacegroupAnalyzer(primitive_structure)
    print(f"   Space group symbol: {sga_primitive.get_space_group_symbol()}")
    print(f"   Space group number: {sga_primitive.get_space_group_number()}")
    print(f"   Crystal system: {sga_primitive.get_crystal_system()}")
    print(f"   Point group: {sga_primitive.get_point_group_symbol()}")
    
    # Compare structures
    print(f"\n7. Comparison Summary:")
    print(f"   Original atoms: {len(structure)}")
    print(f"   Primitive atoms: {len(primitive_structure)}")
    print(f"   Atom count reduction: {len(structure) - len(primitive_structure)} atoms")
    print(f"   Reduction factor: {len(structure) / len(primitive_structure):.2f}x")
    print(f"   Volume ratio: {structure.lattice.volume / primitive_structure.lattice.volume:.2f}")
    
    # Check if space groups match expected Cmma (SG67)
    print(f"\n8. Space Group Verification:")
    original_sg = sga_original.get_space_group_number()
    primitive_sg = sga_primitive.get_space_group_number()
    
    if original_sg == 67:
        print(f"   ✓ Original structure has expected SG67 (Cmma)")
    else:
        print(f"   ⚠ Original structure has SG{original_sg}, not expected SG67 (Cmma)")
        print(f"     This may be due to structural relaxation breaking symmetry")
    
    if primitive_sg == 67:
        print(f"   ✓ Primitive structure maintains SG67 (Cmma)")
    else:
        print(f"   ⚠ Primitive structure has SG{primitive_sg}, not SG67 (Cmma)")
    
    # Try to find the conventional structure with higher symmetry
    print(f"\n9. Attempting Symmetry Enhancement:")
    try:
        # Try with higher tolerance to find symmetry
        sga_refined = SpacegroupAnalyzer(structure, symprec=0.1, angle_tolerance=5)
        refined_structure = sga_refined.get_refined_structure()
        refined_sg = sga_refined.get_space_group_number()
        
        print(f"   Refined space group (tolerance=0.1): SG{refined_sg} ({sga_refined.get_space_group_symbol()})")
        
        if refined_sg == 67:
            print(f"   ✓ Found SG67 with relaxed tolerance!")
            primitive_structure = sga_refined.get_primitive_standard_structure()
            sga_primitive = SpacegroupAnalyzer(primitive_structure)
            print(f"   Updated primitive structure space group: SG{sga_primitive.get_space_group_number()}")
        
    except Exception as e:
        print(f"   Could not enhance symmetry: {e}")
    
    # Save primitive structure
    print(f"\n10. Saving Primitive Structure:")
    output_dir = os.path.dirname(cif_file_path)
    base_name = os.path.basename(cif_file_path).replace('.cif', '')
    primitive_filename = f"{base_name}_primitive.cif"
    primitive_path = os.path.join(output_dir, primitive_filename)
    
    try:
        cif_writer = CifWriter(primitive_structure)
        cif_writer.write_file(primitive_path)
        print(f"   ✓ Primitive structure saved to: {primitive_path}")
    except Exception as e:
        print(f"   ✗ Error saving primitive structure: {e}")
        return None
    
    print(f"\n" + "="*60)
    print("Analysis Complete!")
    print("="*60)
    
    return {
        'original_structure': structure,
        'primitive_structure': primitive_structure,
        'original_sga': sga_original,
        'primitive_sga': sga_primitive,
        'primitive_file': primitive_path
    }

def main():
    """Main function to run the analysis."""
    cif_file = "examples/HfO2/HfO2_SG67_Cmma_relaxed.cif"
    
    if not os.path.exists(cif_file):
        print(f"Error: CIF file not found: {cif_file}")
        sys.exit(1)
    
    results = analyze_structure(cif_file)
    
    if results is None:
        print("Analysis failed!")
        sys.exit(1)
    
    print(f"\nAnalysis completed successfully!")
    print(f"Primitive structure saved to: {results['primitive_file']}")

if __name__ == "__main__":
    main()
