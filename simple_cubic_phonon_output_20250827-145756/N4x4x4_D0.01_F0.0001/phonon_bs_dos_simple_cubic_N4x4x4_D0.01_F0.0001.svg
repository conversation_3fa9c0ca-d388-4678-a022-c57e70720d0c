<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="484.233125pt" height="320.549375pt" viewBox="0 0 484.233125 320.549375" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-27T15:02:34.645745</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 320.549375 
L 484.233125 320.549375 
L 484.233125 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 48.633125 280.8 
L 386.313125 280.8 
L 386.313125 36 
L 48.633125 36 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="mc4a4ea4ba0" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mc4a4ea4ba0" x="48.633125" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- G -->
      <g transform="translate(45.53375 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#mc4a4ea4ba0" x="93.296968" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- X -->
      <g transform="translate(90.556968 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#mc4a4ea4ba0" x="137.960811" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- M -->
      <g transform="translate(134.509561 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#mc4a4ea4ba0" x="201.125024" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- G -->
      <g transform="translate(198.025649 293.87875) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-47"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#mc4a4ea4ba0" x="278.485069" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- R -->
      <g transform="translate(275.705694 293.87875) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-52"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#mc4a4ea4ba0" x="341.649282" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- X|M -->
      <g transform="translate(334.110532 293.91375) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-7c" d="M 1344 4891 
L 1344 -1509 
L 813 -1509 
L 813 4891 
L 1344 4891 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-7c" x="68.505859"/>
       <use xlink:href="#DejaVuSans-4d" x="102.197266"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#mc4a4ea4ba0" x="386.313125" y="280.8" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- R -->
      <g transform="translate(383.53375 293.87875) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-52"/>
      </g>
     </g>
    </g>
    <g id="text_8">
     <!-- Wave vector -->
     <g transform="translate(173.829219 310.437812) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-57"/>
      <use xlink:href="#DejaVuSans-61" x="92.501953"/>
      <use xlink:href="#DejaVuSans-76" x="153.78125"/>
      <use xlink:href="#DejaVuSans-65" x="212.960938"/>
      <use xlink:href="#DejaVuSans-20" x="274.484375"/>
      <use xlink:href="#DejaVuSans-76" x="306.271484"/>
      <use xlink:href="#DejaVuSans-65" x="365.451172"/>
      <use xlink:href="#DejaVuSans-63" x="426.974609"/>
      <use xlink:href="#DejaVuSans-74" x="481.955078"/>
      <use xlink:href="#DejaVuSans-6f" x="521.164062"/>
      <use xlink:href="#DejaVuSans-72" x="582.345703"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_8">
      <defs>
       <path id="m60f96e543a" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m60f96e543a" x="48.633125" y="273.39439" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- −10 -->
      <g transform="translate(24.749375 276.433765) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
       <use xlink:href="#DejaVuSans-30" x="147.412109"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m60f96e543a" x="48.633125" y="238.467099" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- −5 -->
      <g transform="translate(29.839375 241.506474) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-35" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_10">
      <g>
       <use xlink:href="#m60f96e543a" x="48.633125" y="203.539808" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0 -->
      <g transform="translate(36.543125 206.579183) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_11">
      <g>
       <use xlink:href="#m60f96e543a" x="48.633125" y="168.612517" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 5 -->
      <g transform="translate(36.543125 171.651892) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_12">
      <g>
       <use xlink:href="#m60f96e543a" x="48.633125" y="133.685226" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 10 -->
      <g transform="translate(31.453125 136.724601) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-30" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_13">
      <g>
       <use xlink:href="#m60f96e543a" x="48.633125" y="98.757934" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 15 -->
      <g transform="translate(31.453125 101.797309) scale(0.08 -0.08)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-35" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_14">
      <g>
       <use xlink:href="#m60f96e543a" x="48.633125" y="63.830643" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 20 -->
      <g transform="translate(31.453125 66.870018) scale(0.08 -0.08)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
       <use xlink:href="#DejaVuSans-30" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="text_16">
     <!-- Frequency (THz) -->
     <g transform="translate(17.837813 215.462031) rotate(-90) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-72" x="50.269531"/>
      <use xlink:href="#DejaVuSans-65" x="89.132812"/>
      <use xlink:href="#DejaVuSans-71" x="150.65625"/>
      <use xlink:href="#DejaVuSans-75" x="214.132812"/>
      <use xlink:href="#DejaVuSans-65" x="277.511719"/>
      <use xlink:href="#DejaVuSans-6e" x="339.035156"/>
      <use xlink:href="#DejaVuSans-63" x="402.414062"/>
      <use xlink:href="#DejaVuSans-79" x="457.394531"/>
      <use xlink:href="#DejaVuSans-20" x="516.574219"/>
      <use xlink:href="#DejaVuSans-28" x="548.361328"/>
      <use xlink:href="#DejaVuSans-54" x="587.375"/>
      <use xlink:href="#DejaVuSans-48" x="648.458984"/>
      <use xlink:href="#DejaVuSans-7a" x="723.654297"/>
      <use xlink:href="#DejaVuSans-29" x="776.144531"/>
     </g>
    </g>
   </g>
   <g id="line2d_15">
    <path d="M 48.633125 232.795303 
L 52.355112 232.892341 
L 56.077099 233.177767 
L 59.799086 233.63399 
L 63.521073 234.230437 
L 67.24306 234.923058 
L 70.965047 235.65562 
L 74.687033 236.363508 
L 78.40902 236.980356 
L 82.131007 237.448075 
L 85.852994 237.732487 
L 89.574981 237.847413 
L 93.296968 237.867148 
L 97.018955 237.975803 
L 100.740942 238.293973 
L 104.462929 238.798864 
L 108.184916 239.454652 
L 111.906903 240.215363 
L 115.62889 241.028361 
L 119.350877 241.838112 
L 123.072863 243.013581 
L 126.79485 244.183181 
L 130.516837 245.077139 
L 134.238824 245.637642 
L 137.960811 245.828571 
L 141.469934 245.659972 
L 144.979057 245.165836 
L 148.48818 244.395711 
L 151.997303 243.484435 
L 155.506426 242.585262 
L 159.015549 241.680771 
L 162.524672 240.725791 
L 166.033795 239.71966 
L 169.542917 238.683358 
L 173.05204 237.646732 
L 176.561163 236.642864 
L 180.070286 235.704616 
L 183.579409 234.862124 
L 187.088532 234.141072 
L 190.597655 233.561729 
L 194.106778 233.138736 
L 197.615901 232.881551 
L 201.125024 232.795303 
L 204.488504 230.231838 
L 207.851984 222.415482 
L 211.215465 196.639008 
L 214.578945 186.319859 
L 217.942425 184.534596 
L 221.305905 183.462227 
L 224.669385 182.428704 
L 228.032866 180.731514 
L 231.396346 178.044291 
L 234.759826 174.571553 
L 238.123306 170.810416 
L 241.486787 167.340973 
L 244.850267 164.379451 
L 248.213747 161.718761 
L 251.577227 159.129639 
L 254.940708 156.350365 
L 258.304188 153.224277 
L 261.667668 149.784612 
L 265.031148 146.215583 
L 268.394629 142.742088 
L 271.758109 139.534293 
L 275.121589 136.674604 
L 278.485069 134.471415 
L 281.64328 136.210031 
L 284.801491 138.482107 
L 287.959701 140.986188 
L 291.117912 143.70878 
L 294.276122 146.652836 
L 297.434333 149.841451 
L 300.592544 153.315901 
L 303.750754 157.127372 
L 306.908965 161.33053 
L 310.067176 165.988341 
L 313.225386 171.19848 
L 316.383597 177.166254 
L 319.541807 184.655036 
L 322.700018 208.778208 
L 325.858229 222.693138 
L 329.016439 229.101884 
L 332.17465 233.221957 
L 335.332861 235.876725 
L 338.491071 237.379509 
L 341.649282 237.867148 
L 341.649282 245.828571 
L 345.084962 244.546524 
L 348.520642 240.544622 
L 351.956323 233.126045 
L 355.392003 220.452574 
L 358.827683 183.626818 
L 362.263363 169.597938 
L 365.699044 159.51821 
L 369.134724 151.497441 
L 372.570404 145.080323 
L 376.006084 140.122678 
L 379.441765 138.077711 
L 382.877445 136.193653 
L 386.313125 134.471415 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_16">
    <path d="M 48.633125 232.795303 
L 52.355112 232.892319 
L 56.077099 233.177748 
L 59.799086 233.633974 
L 63.521073 234.230424 
L 67.24306 234.923048 
L 70.965047 235.655612 
L 74.687033 236.363502 
L 78.40902 236.98035 
L 82.131007 237.448067 
L 85.852994 237.732476 
L 89.574981 237.847399 
L 93.296968 237.867131 
L 97.018955 236.263846 
L 100.740942 235.129238 
L 104.462929 236.065444 
L 108.184916 237.276744 
L 111.906903 238.676065 
L 115.62889 240.165744 
L 119.350877 241.644446 
L 123.072863 242.589935 
L 126.79485 243.233544 
L 130.516837 243.726211 
L 134.238824 244.035403 
L 137.960811 244.140775 
L 141.469934 244.045375 
L 144.979057 243.751818 
L 148.48818 243.223577 
L 151.997303 242.347824 
L 155.506426 241.00253 
L 159.015549 239.24544 
L 162.524672 237.170449 
L 166.033795 234.833862 
L 169.542917 232.275013 
L 173.05204 229.526287 
L 176.561163 226.615825 
L 180.070286 223.568389 
L 183.579409 220.405937 
L 187.088532 217.148323 
L 190.597655 213.814133 
L 194.106778 216.311513 
L 197.615901 229.142386 
L 201.125024 232.795303 
L 204.488504 230.217924 
L 207.851984 222.290018 
L 211.215465 195.894626 
L 214.578945 183.515115 
L 217.942425 177.337907 
L 221.305905 173.788768 
L 224.669385 172.314588 
L 228.032866 172.313358 
L 231.396346 172.505701 
L 234.759826 171.645537 
L 238.123306 169.34041 
L 241.486787 166.174422 
L 244.850267 162.657361 
L 248.213747 159.002861 
L 251.577227 155.396032 
L 254.940708 151.882516 
L 258.304188 148.480732 
L 261.667668 145.242179 
L 265.031148 142.254963 
L 268.394629 139.609153 
L 271.758109 137.365928 
L 275.121589 135.56254 
L 278.485069 134.471411 
L 281.64328 136.16083 
L 284.801491 138.371614 
L 287.959701 140.80629 
L 291.117912 143.45342 
L 294.276122 146.308394 
L 297.434333 149.377024 
L 300.592544 152.684639 
L 303.750754 156.284463 
L 306.908965 160.264098 
L 310.067176 164.752961 
L 313.225386 169.942333 
L 316.383597 176.157687 
L 319.541807 184.448662 
L 322.700018 199.723784 
L 325.858229 221.385798 
L 329.016439 228.264716 
L 332.17465 232.73027 
L 335.332861 235.649674 
L 338.491071 237.321338 
L 341.649282 237.867131 
L 341.649282 244.140775 
L 345.084962 243.005904 
L 348.520642 239.450759 
L 351.956323 232.835811 
L 355.392003 218.899087 
L 358.827683 181.097813 
L 362.263363 167.573609 
L 365.699044 157.887411 
L 369.134724 150.347519 
L 372.570404 144.464383 
L 376.006084 140.026235 
L 379.441765 138.07771 
L 382.877445 136.193652 
L 386.313125 134.471411 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_17">
    <path d="M 48.633125 232.795269 
L 52.355112 215.48981 
L 56.077099 210.800207 
L 59.799086 214.33655 
L 63.521073 217.753956 
L 67.24306 221.006136 
L 70.965047 224.042727 
L 74.687033 226.810063 
L 78.40902 229.251852 
L 82.131007 231.308175 
L 85.852994 232.909298 
L 89.574981 233.960216 
L 93.296968 234.335 
L 97.018955 234.537452 
L 100.740942 231.071497 
L 104.462929 219.916739 
L 108.184916 184.76036 
L 111.906903 171.231382 
L 115.62889 161.899028 
L 119.350877 155.06095 
L 123.072863 150.292095 
L 126.79485 147.258922 
L 130.516837 145.537739 
L 134.238824 144.703466 
L 137.960811 144.45988 
L 141.469934 144.949003 
L 144.979057 146.449134 
L 148.48818 149.018883 
L 151.997303 152.639723 
L 155.506426 157.075287 
L 159.015549 161.713769 
L 162.524672 165.483923 
L 166.033795 167.159009 
L 169.542917 166.321326 
L 173.05204 164.102897 
L 176.561163 163.460696 
L 180.070286 167.176198 
L 183.579409 171.132 
L 187.088532 175.78503 
L 190.597655 183.915888 
L 194.106778 210.421534 
L 197.615901 222.329516 
L 201.125024 232.795269 
L 204.488504 224.392216 
L 207.851984 193.320285 
L 211.215465 189.208205 
L 214.578945 183.060359 
L 217.942425 176.788669 
L 221.305905 173.060822 
L 224.669385 171.33922 
L 228.032866 171.141598 
L 231.396346 171.37881 
L 234.759826 170.863095 
L 238.123306 169.07414 
L 241.486787 165.93116 
L 244.850267 162.044471 
L 248.213747 158.180284 
L 251.577227 154.518309 
L 254.940708 151.08033 
L 258.304188 147.843091 
L 261.667668 144.805317 
L 265.031148 142.004561 
L 268.394629 139.496789 
L 271.758109 137.332382 
L 275.121589 135.558706 
L 278.485069 134.471411 
L 281.64328 134.728503 
L 284.801491 135.497212 
L 287.959701 136.769854 
L 291.117912 138.795946 
L 294.276122 141.500678 
L 297.434333 144.709845 
L 300.592544 148.404501 
L 303.750754 152.588046 
L 306.908965 157.292895 
L 310.067176 162.591224 
L 313.225386 168.623262 
L 316.383597 175.689195 
L 319.541807 184.179089 
L 322.700018 195.964096 
L 325.858229 218.40591 
L 329.016439 225.104371 
L 332.17465 229.37655 
L 335.332861 232.182146 
L 338.491071 233.802736 
L 341.649282 234.335 
L 341.649282 144.45988 
L 345.084962 144.473622 
L 348.520642 144.503055 
L 351.956323 144.514466 
L 355.392003 144.457088 
L 358.827683 144.270705 
L 362.263363 143.895028 
L 365.699044 143.279286 
L 369.134724 142.390132 
L 372.570404 141.216179 
L 376.006084 139.768464 
L 379.441765 136.92031 
L 382.877445 135.080728 
L 386.313125 134.471411 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_18">
    <path d="M 48.633125 203.401747 
L 52.355112 207.186644 
L 56.077099 210.800175 
L 59.799086 214.336536 
L 63.521073 217.753952 
L 67.24306 221.006133 
L 70.965047 224.042722 
L 74.687033 226.810056 
L 78.40902 229.251846 
L 82.131007 231.308171 
L 85.852994 232.909298 
L 89.574981 233.960211 
L 93.296968 234.334993 
L 97.018955 232.406896 
L 100.740942 225.962213 
L 104.462929 202.051349 
L 108.184916 178.615861 
L 111.906903 167.701553 
L 115.62889 159.686873 
L 119.350877 153.748348 
L 123.072863 149.566809 
L 126.79485 146.879879 
L 130.516837 145.365404 
L 134.238824 144.657795 
L 137.960811 144.4598 
L 141.469934 143.885171 
L 144.979057 142.515278 
L 148.48818 141.16059 
L 151.997303 140.606962 
L 155.506426 141.324958 
L 159.015549 143.435744 
L 162.524672 146.763304 
L 166.033795 150.898394 
L 169.542917 155.315886 
L 173.05204 159.564037 
L 176.561163 162.589788 
L 180.070286 163.517418 
L 183.579409 167.303064 
L 187.088532 173.769492 
L 190.597655 181.441325 
L 194.106778 188.21644 
L 197.615901 206.988534 
L 201.125024 203.401747 
L 204.488504 197.976191 
L 207.851984 184.699495 
L 211.215465 175.443256 
L 214.578945 167.794679 
L 217.942425 162.64846 
L 221.305905 159.636661 
L 224.669385 157.626906 
L 228.032866 155.529948 
L 231.396346 152.958651 
L 234.759826 150.014633 
L 238.123306 146.938413 
L 241.486787 144.110519 
L 244.850267 141.698554 
L 248.213747 139.637459 
L 251.577227 137.893956 
L 254.940708 136.392117 
L 258.304188 135.081007 
L 261.667668 133.97283 
L 265.031148 133.138277 
L 268.394629 132.67238 
L 271.758109 132.652298 
L 275.121589 133.101255 
L 278.485069 133.746129 
L 281.64328 134.07267 
L 284.801491 135.042741 
L 287.959701 136.629763 
L 291.117912 138.533851 
L 294.276122 140.772579 
L 297.434333 143.467382 
L 300.592544 146.601206 
L 303.750754 150.164097 
L 306.908965 154.16065 
L 310.067176 158.6199 
L 313.225386 163.610014 
L 316.383597 169.26694 
L 319.541807 175.872552 
L 322.700018 184.172417 
L 325.858229 199.879439 
L 329.016439 221.088487 
L 332.17465 227.544708 
L 335.332861 231.448962 
L 338.491071 233.628891 
L 341.649282 234.334993 
L 341.649282 144.4598 
L 345.084962 144.473544 
L 348.520642 144.502982 
L 351.956323 144.514401 
L 355.392003 144.457033 
L 358.827683 144.270661 
L 362.263363 143.894996 
L 365.699044 143.279264 
L 369.134724 142.390119 
L 372.570404 141.216173 
L 376.006084 139.768461 
L 379.441765 136.582958 
L 382.877445 134.455972 
L 386.313125 133.746129 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_19">
    <path d="M 48.633125 203.398498 
L 52.355112 207.186566 
L 56.077099 177.8761 
L 59.799086 166.233576 
L 63.521073 156.23471 
L 67.24306 147.972466 
L 70.965047 141.341897 
L 74.687033 135.986131 
L 78.40902 131.372458 
L 82.131007 126.996379 
L 85.852994 122.719577 
L 89.574981 119.154836 
L 93.296968 117.675598 
L 97.018955 117.731187 
L 100.740942 117.888229 
L 104.462929 118.115029 
L 108.184916 118.351109 
L 111.906903 118.499421 
L 115.62889 118.427153 
L 119.350877 117.996237 
L 123.072863 117.145434 
L 126.79485 115.988605 
L 130.516837 114.813529 
L 134.238824 113.950162 
L 137.960811 113.634862 
L 141.469934 113.800357 
L 144.979057 114.390146 
L 148.48818 115.631547 
L 151.997303 117.756754 
L 155.506426 120.864048 
L 159.015549 124.84061 
L 162.524672 129.379905 
L 166.033795 134.075749 
L 169.542917 138.539545 
L 173.05204 142.479843 
L 176.561163 145.756902 
L 180.070286 148.714048 
L 183.579409 152.667564 
L 187.088532 159.130922 
L 190.597655 168.702522 
L 194.106778 180.420865 
L 197.615901 195.356119 
L 201.125024 203.398498 
L 204.488504 194.173383 
L 207.851984 184.579857 
L 211.215465 175.133177 
L 214.578945 167.237572 
L 217.942425 161.847326 
L 221.305905 158.664877 
L 224.669385 156.639949 
L 228.032866 154.679378 
L 231.396346 152.31962 
L 234.759826 149.621682 
L 238.123306 146.808191 
L 241.486787 143.986298 
L 244.850267 141.360888 
L 248.213747 139.157031 
L 251.577227 137.356502 
L 254.940708 135.878514 
L 258.304188 134.651068 
L 261.667668 133.657865 
L 265.031148 132.941468 
L 268.394629 132.57397 
L 271.758109 132.618435 
L 275.121589 133.096276 
L 278.485069 133.746129 
L 281.64328 132.25468 
L 284.801491 130.630474 
L 287.959701 129.151262 
L 291.117912 127.802957 
L 294.276122 126.568246 
L 297.434333 125.433395 
L 300.592544 124.388546 
L 303.750754 123.426435 
L 306.908965 122.541074 
L 310.067176 121.727359 
L 313.225386 120.981723 
L 316.383597 120.303194 
L 319.541807 119.693875 
L 322.700018 119.158275 
L 325.858229 118.701669 
L 329.016439 118.328262 
L 332.17465 118.039985 
L 335.332861 117.836399 
L 338.491071 117.715594 
L 341.649282 117.675598 
L 341.649282 113.634862 
L 345.084962 113.808471 
L 348.520642 114.323907 
L 351.956323 115.165491 
L 355.392003 116.308625 
L 358.827683 117.721781 
L 362.263363 119.368731 
L 365.699044 121.210623 
L 369.134724 123.20753 
L 372.570404 125.319211 
L 376.006084 127.504936 
L 379.441765 129.722046 
L 382.877445 131.919843 
L 386.313125 133.746129 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_20">
    <path d="M 48.633125 203.398498 
L 52.355112 189.696456 
L 56.077099 159.448147 
L 59.799086 133.037271 
L 63.521073 111.52932 
L 67.24306 94.566287 
L 70.965047 82.246929 
L 74.687033 74.513281 
L 78.40902 71.001017 
L 82.131007 70.971429 
L 85.852994 73.239006 
L 89.574981 76.074881 
L 93.296968 77.391543 
L 97.018955 78.176528 
L 100.740942 80.459563 
L 104.462929 84.030783 
L 108.184916 88.560072 
L 111.906903 93.626609 
L 115.62889 98.760607 
L 119.350877 103.498481 
L 123.072863 107.453333 
L 126.79485 110.394843 
L 130.516837 112.301509 
L 134.238824 113.32104 
L 137.960811 113.634813 
L 141.469934 113.164916 
L 144.979057 111.80641 
L 148.48818 109.725832 
L 151.997303 107.202271 
L 155.506426 104.550049 
L 159.015549 102.021399 
L 162.524672 99.810797 
L 166.033795 98.160521 
L 169.542917 97.464165 
L 173.05204 98.293357 
L 176.561163 101.340146 
L 180.070286 107.306508 
L 183.579409 116.785859 
L 187.088532 130.191282 
L 190.597655 147.842168 
L 194.106778 170.850884 
L 197.615901 192.174675 
L 201.125024 203.398498 
L 204.488504 194.147787 
L 207.851984 176.187395 
L 211.215465 154.115241 
L 214.578945 137.647738 
L 217.942425 125.178428 
L 221.305905 116.267711 
L 224.669385 110.484151 
L 228.032866 107.298069 
L 231.396346 106.136435 
L 234.759826 106.461077 
L 238.123306 107.821135 
L 241.486787 109.863248 
L 244.850267 112.308901 
L 248.213747 114.926299 
L 251.577227 117.522765 
L 254.940708 119.961169 
L 258.304188 122.179859 
L 261.667668 124.193771 
L 265.031148 126.073013 
L 268.394629 127.911954 
L 271.758109 129.804167 
L 275.121589 131.827745 
L 278.485069 133.746117 
L 281.64328 132.206416 
L 284.801491 130.500831 
L 287.959701 128.848581 
L 291.117912 127.151938 
L 294.276122 125.298461 
L 297.434333 123.18552 
L 300.592544 120.738275 
L 303.750754 117.918177 
L 306.908965 114.719857 
L 310.067176 111.161214 
L 313.225386 107.275642 
L 316.383597 103.113196 
L 319.541807 98.751245 
L 322.700018 94.30877 
L 325.858229 89.955288 
L 329.016439 85.906905 
L 332.17465 82.407246 
L 335.332861 79.697049 
L 338.491071 77.979847 
L 341.649282 77.391543 
L 341.649282 113.634813 
L 345.084962 113.808423 
L 348.520642 114.323862 
L 351.956323 115.165451 
L 355.392003 116.308592 
L 358.827683 117.721755 
L 362.263363 119.368712 
L 365.699044 121.210612 
L 369.134724 123.207524 
L 372.570404 125.31921 
L 376.006084 127.504934 
L 379.441765 129.722043 
L 382.877445 131.919839 
L 386.313125 133.746117 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke: #0000ff; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_21">
    <path d="M 48.633125 280.8 
L 48.633125 36 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_22">
    <path d="M 93.296968 280.8 
L 93.296968 36 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_23">
    <path d="M 137.960811 280.8 
L 137.960811 36 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_24">
    <path d="M 201.125024 280.8 
L 201.125024 36 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_25">
    <path d="M 278.485069 280.8 
L 278.485069 36 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_26">
    <path d="M 341.649282 280.8 
L 341.649282 36 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_27">
    <path d="M 341.649282 280.8 
L 341.649282 36 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="line2d_28">
    <path d="M 386.313125 280.8 
L 386.313125 36 
" clip-path="url(#pc6c6e130e5)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5"/>
   </g>
   <g id="patch_3">
    <path d="M 48.633125 280.8 
L 48.633125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 386.313125 280.8 
L 386.313125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 48.633125 280.8 
L 386.313125 280.8 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 48.633125 36 
L 386.313125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_7">
    <path d="M 391.353125 280.8 
L 477.033125 280.8 
L 477.033125 36 
L 391.353125 36 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m05af1396ef" d="M 395.252651 -117.009567 
L 395.252651 -74.379164 
L 398.026657 -75.264631 
L 400.832398 -76.150098 
L 406.367995 -77.035565 
L 411.794892 -77.921032 
L 416.404584 -78.806499 
L 421.778792 -79.691966 
L 424.848923 -80.577433 
L 434.228891 -81.4629 
L 444.976713 -82.348367 
L 445.40881 -83.233834 
L 440.918897 -84.119301 
L 441.795596 -85.004768 
L 443.966028 -85.890235 
L 441.131638 -86.775702 
L 439.068529 -87.661169 
L 436.618069 -88.546636 
L 431.883904 -89.432103 
L 430.425523 -90.31757 
L 428.002763 -91.203037 
L 425.391062 -92.088504 
L 421.679095 -92.973971 
L 423.122332 -93.859438 
L 425.512499 -94.744905 
L 422.683096 -95.630372 
L 420.87959 -96.515839 
L 420.771319 -97.401306 
L 417.283253 -98.286773 
L 415.513953 -99.17224 
L 416.718904 -100.057707 
L 412.448043 -100.943174 
L 412.06857 -101.828641 
L 412.319733 -102.714108 
L 411.626155 -103.599575 
L 411.028348 -104.485042 
L 409.782666 -105.370509 
L 406.556203 -106.255976 
L 405.763989 -107.141443 
L 406.269731 -108.02691 
L 407.019966 -108.912377 
L 402.655797 -109.797844 
L 403.072435 -110.683311 
L 401.966524 -111.568778 
L 400.034542 -112.454245 
L 399.727301 -113.339712 
L 399.149263 -114.225179 
L 397.013935 -115.110646 
L 396.419516 -115.996113 
L 395.384382 -116.88158 
L 396.087022 -117.767047 
L 397.742146 -118.652514 
L 397.855519 -119.537981 
L 399.807371 -120.423448 
L 399.444783 -121.308915 
L 401.286183 -122.194382 
L 402.284446 -123.079849 
L 404.372441 -123.965316 
L 404.138352 -124.850783 
L 404.244454 -125.73625 
L 405.459939 -126.621717 
L 407.572451 -127.507184 
L 406.304668 -128.392651 
L 408.967586 -129.278118 
L 405.809006 -130.163585 
L 408.462886 -131.049052 
L 409.184687 -131.934519 
L 410.384034 -132.819986 
L 409.791904 -133.705453 
L 411.597308 -134.59092 
L 411.948005 -135.476387 
L 409.28532 -136.361854 
L 415.339316 -137.247321 
L 417.6878 -138.132788 
L 420.324518 -139.018255 
L 421.253646 -139.903722 
L 413.615659 -140.789189 
L 413.9714 -141.674656 
L 416.880014 -142.560123 
L 415.477935 -143.44559 
L 421.627441 -144.331057 
L 417.7753 -145.216524 
L 416.790138 -146.101991 
L 424.529845 -146.987458 
L 431.263714 -147.872925 
L 428.285343 -148.758392 
L 425.052638 -149.643859 
L 420.272032 -150.529326 
L 425.99389 -151.414793 
L 433.187323 -152.30026 
L 435.356245 -153.185727 
L 451.952578 -154.071194 
L 428.193755 -154.956661 
L 421.700763 -155.842128 
L 425.532664 -156.727595 
L 422.946059 -157.613062 
L 427.711633 -158.498529 
L 429.531955 -159.383996 
L 429.488881 -160.269463 
L 426.064068 -161.15493 
L 421.306749 -162.040397 
L 425.678951 -162.925864 
L 430.386448 -163.811331 
L 436.03553 -164.696798 
L 428.409988 -165.582265 
L 423.31266 -166.467732 
L 429.313356 -167.353199 
L 433.618469 -168.238666 
L 438.339835 -169.124133 
L 429.771205 -170.0096 
L 432.781311 -170.895067 
L 437.32299 -171.780534 
L 435.228609 -172.666001 
L 446.682133 -173.551468 
L 444.278125 -174.436935 
L 453.321791 -175.322402 
L 473.13858 -176.207869 
L 448.006921 -177.093336 
L 423.788777 -177.978803 
L 419.879635 -178.86427 
L 415.454297 -179.749737 
L 419.299232 -180.635204 
L 408.742979 -181.520671 
L 409.803053 -182.406138 
L 411.813084 -183.291605 
L 415.621918 -184.177072 
L 407.075786 -185.062539 
L 408.187934 -185.948006 
L 410.166628 -186.833473 
L 409.404651 -187.71894 
L 409.389188 -188.604407 
L 414.341289 -189.489874 
L 421.823209 -190.375341 
L 416.074348 -191.260808 
L 420.225907 -192.146275 
L 425.554194 -193.031742 
L 425.070367 -193.917209 
L 428.53593 -194.802676 
L 431.902814 -195.688143 
L 427.772479 -196.57361 
L 433.131274 -197.459077 
L 434.558513 -198.344544 
L 443.819684 -199.230011 
L 444.447788 -200.115478 
L 447.135934 -201.000945 
L 447.086639 -201.886412 
L 427.469205 -202.771879 
L 419.631903 -203.657346 
L 422.124269 -204.542813 
L 416.194074 -205.42828 
L 417.428165 -206.313747 
L 417.805887 -207.199214 
L 414.419623 -208.084681 
L 414.641886 -208.970148 
L 411.095089 -209.855615 
L 412.506779 -210.741082 
L 411.819704 -211.626549 
L 415.568587 -212.512016 
L 415.412238 -213.397483 
L 413.667642 -214.28295 
L 414.330244 -215.168417 
L 416.580578 -216.053884 
L 410.200296 -216.939351 
L 415.595841 -217.824818 
L 413.36309 -218.710285 
L 412.290074 -219.595752 
L 416.334928 -220.481219 
L 412.155233 -221.366686 
L 412.377199 -222.252153 
L 411.164799 -223.13762 
L 408.92532 -224.023087 
L 407.861365 -224.908554 
L 405.735241 -225.794021 
L 407.354568 -226.679488 
L 405.63699 -227.564955 
L 405.538692 -228.450422 
L 405.723894 -229.335889 
L 403.730875 -230.221356 
L 406.022188 -231.106823 
L 402.670929 -231.99229 
L 402.170224 -232.877757 
L 402.677421 -233.763224 
L 402.361369 -234.648691 
L 400.628249 -235.534158 
L 403.019549 -236.419625 
L 401.024969 -237.305092 
L 400.612588 -238.190559 
L 400.436145 -239.076026 
L 402.646787 -239.961493 
L 402.084032 -240.84696 
L 399.186645 -241.732427 
L 400.95127 -242.617894 
L 400.462111 -243.503361 
L 399.72011 -244.388828 
L 398.231897 -245.274295 
L 399.222683 -246.159762 
L 396.907551 -247.045229 
L 397.875812 -247.930696 
L 396.289979 -248.816163 
L 396.150035 -249.70163 
L 395.24767 -250.587097 
L 395.24767 -117.009567 
L 395.24767 -117.009567 
L 396.150035 -117.009567 
L 396.289979 -117.009567 
L 397.875812 -117.009567 
L 396.907551 -117.009567 
L 399.222683 -117.009567 
L 398.231897 -117.009567 
L 399.72011 -117.009567 
L 400.462111 -117.009567 
L 400.95127 -117.009567 
L 399.186645 -117.009567 
L 402.084032 -117.009567 
L 402.646787 -117.009567 
L 400.436145 -117.009567 
L 400.612588 -117.009567 
L 401.024969 -117.009567 
L 403.019549 -117.009567 
L 400.628249 -117.009567 
L 402.361369 -117.009567 
L 402.677421 -117.009567 
L 402.170224 -117.009567 
L 402.670929 -117.009567 
L 406.022188 -117.009567 
L 403.730875 -117.009567 
L 405.723894 -117.009567 
L 405.538692 -117.009567 
L 405.63699 -117.009567 
L 407.354568 -117.009567 
L 405.735241 -117.009567 
L 407.861365 -117.009567 
L 408.92532 -117.009567 
L 411.164799 -117.009567 
L 412.377199 -117.009567 
L 412.155233 -117.009567 
L 416.334928 -117.009567 
L 412.290074 -117.009567 
L 413.36309 -117.009567 
L 415.595841 -117.009567 
L 410.200296 -117.009567 
L 416.580578 -117.009567 
L 414.330244 -117.009567 
L 413.667642 -117.009567 
L 415.412238 -117.009567 
L 415.568587 -117.009567 
L 411.819704 -117.009567 
L 412.506779 -117.009567 
L 411.095089 -117.009567 
L 414.641886 -117.009567 
L 414.419623 -117.009567 
L 417.805887 -117.009567 
L 417.428165 -117.009567 
L 416.194074 -117.009567 
L 422.124269 -117.009567 
L 419.631903 -117.009567 
L 427.469205 -117.009567 
L 447.086639 -117.009567 
L 447.135934 -117.009567 
L 444.447788 -117.009567 
L 443.819684 -117.009567 
L 434.558513 -117.009567 
L 433.131274 -117.009567 
L 427.772479 -117.009567 
L 431.902814 -117.009567 
L 428.53593 -117.009567 
L 425.070367 -117.009567 
L 425.554194 -117.009567 
L 420.225907 -117.009567 
L 416.074348 -117.009567 
L 421.823209 -117.009567 
L 414.341289 -117.009567 
L 409.389188 -117.009567 
L 409.404651 -117.009567 
L 410.166628 -117.009567 
L 408.187934 -117.009567 
L 407.075786 -117.009567 
L 415.621918 -117.009567 
L 411.813084 -117.009567 
L 409.803053 -117.009567 
L 408.742979 -117.009567 
L 419.299232 -117.009567 
L 415.454297 -117.009567 
L 419.879635 -117.009567 
L 423.788777 -117.009567 
L 448.006921 -117.009567 
L 473.13858 -117.009567 
L 453.321791 -117.009567 
L 444.278125 -117.009567 
L 446.682133 -117.009567 
L 435.228609 -117.009567 
L 437.32299 -117.009567 
L 432.781311 -117.009567 
L 429.771205 -117.009567 
L 438.339835 -117.009567 
L 433.618469 -117.009567 
L 429.313356 -117.009567 
L 423.31266 -117.009567 
L 428.409988 -117.009567 
L 436.03553 -117.009567 
L 430.386448 -117.009567 
L 425.678951 -117.009567 
L 421.306749 -117.009567 
L 426.064068 -117.009567 
L 429.488881 -117.009567 
L 429.531955 -117.009567 
L 427.711633 -117.009567 
L 422.946059 -117.009567 
L 425.532664 -117.009567 
L 421.700763 -117.009567 
L 428.193755 -117.009567 
L 451.952578 -117.009567 
L 435.356245 -117.009567 
L 433.187323 -117.009567 
L 425.99389 -117.009567 
L 420.272032 -117.009567 
L 425.052638 -117.009567 
L 428.285343 -117.009567 
L 431.263714 -117.009567 
L 424.529845 -117.009567 
L 416.790138 -117.009567 
L 417.7753 -117.009567 
L 421.627441 -117.009567 
L 415.477935 -117.009567 
L 416.880014 -117.009567 
L 413.9714 -117.009567 
L 413.615659 -117.009567 
L 421.253646 -117.009567 
L 420.324518 -117.009567 
L 417.6878 -117.009567 
L 415.339316 -117.009567 
L 409.28532 -117.009567 
L 411.948005 -117.009567 
L 411.597308 -117.009567 
L 409.791904 -117.009567 
L 410.384034 -117.009567 
L 409.184687 -117.009567 
L 408.462886 -117.009567 
L 405.809006 -117.009567 
L 408.967586 -117.009567 
L 406.304668 -117.009567 
L 407.572451 -117.009567 
L 405.459939 -117.009567 
L 404.244454 -117.009567 
L 404.138352 -117.009567 
L 404.372441 -117.009567 
L 402.284446 -117.009567 
L 401.286183 -117.009567 
L 399.444783 -117.009567 
L 399.807371 -117.009567 
L 397.855519 -117.009567 
L 397.742146 -117.009567 
L 396.087022 -117.009567 
L 395.384382 -117.009567 
L 396.419516 -117.009567 
L 397.013935 -117.009567 
L 399.149263 -117.009567 
L 399.727301 -117.009567 
L 400.034542 -117.009567 
L 401.966524 -117.009567 
L 403.072435 -117.009567 
L 402.655797 -117.009567 
L 407.019966 -117.009567 
L 406.269731 -117.009567 
L 405.763989 -117.009567 
L 406.556203 -117.009567 
L 409.782666 -117.009567 
L 411.028348 -117.009567 
L 411.626155 -117.009567 
L 412.319733 -117.009567 
L 412.06857 -117.009567 
L 412.448043 -117.009567 
L 416.718904 -117.009567 
L 415.513953 -117.009567 
L 417.283253 -117.009567 
L 420.771319 -117.009567 
L 420.87959 -117.009567 
L 422.683096 -117.009567 
L 425.512499 -117.009567 
L 423.122332 -117.009567 
L 421.679095 -117.009567 
L 425.391062 -117.009567 
L 428.002763 -117.009567 
L 430.425523 -117.009567 
L 431.883904 -117.009567 
L 436.618069 -117.009567 
L 439.068529 -117.009567 
L 441.131638 -117.009567 
L 443.966028 -117.009567 
L 441.795596 -117.009567 
L 440.918897 -117.009567 
L 445.40881 -117.009567 
L 444.976713 -117.009567 
L 434.228891 -117.009567 
L 424.848923 -117.009567 
L 421.778792 -117.009567 
L 416.404584 -117.009567 
L 411.794892 -117.009567 
L 406.367995 -117.009567 
L 400.832398 -117.009567 
L 398.026657 -117.009567 
L 395.252651 -117.009567 
z
" style="stroke: #000000"/>
    </defs>
    <g clip-path="url(#p0e9486cf16)">
     <use xlink:href="#m05af1396ef" x="0" y="320.549375" style="fill: #808080; stroke: #000000"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="text_17">
     <!-- DOS -->
     <g transform="translate(418.848906 295.437812) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-44"/>
      <use xlink:href="#DejaVuSans-4f" x="77.001953"/>
      <use xlink:href="#DejaVuSans-53" x="155.712891"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4"/>
   <g id="patch_8">
    <path d="M 391.353125 280.8 
L 391.353125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 477.033125 280.8 
L 477.033125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 391.353125 280.8 
L 477.033125 280.8 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_11">
    <path d="M 391.353125 36 
L 477.033125 36 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
  <g id="text_18">
   <!-- Phonon band structure and DOS of FLi (simple_cubic) with (4x4x4) supercell -->
   <g transform="translate(11.29625 16.318125) scale(0.12 -0.12)">
    <defs>
     <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#DejaVuSans-50"/>
    <use xlink:href="#DejaVuSans-68" x="60.302734"/>
    <use xlink:href="#DejaVuSans-6f" x="123.681641"/>
    <use xlink:href="#DejaVuSans-6e" x="184.863281"/>
    <use xlink:href="#DejaVuSans-6f" x="248.242188"/>
    <use xlink:href="#DejaVuSans-6e" x="309.423828"/>
    <use xlink:href="#DejaVuSans-20" x="372.802734"/>
    <use xlink:href="#DejaVuSans-62" x="404.589844"/>
    <use xlink:href="#DejaVuSans-61" x="468.066406"/>
    <use xlink:href="#DejaVuSans-6e" x="529.345703"/>
    <use xlink:href="#DejaVuSans-64" x="592.724609"/>
    <use xlink:href="#DejaVuSans-20" x="656.201172"/>
    <use xlink:href="#DejaVuSans-73" x="687.988281"/>
    <use xlink:href="#DejaVuSans-74" x="740.087891"/>
    <use xlink:href="#DejaVuSans-72" x="779.296875"/>
    <use xlink:href="#DejaVuSans-75" x="820.410156"/>
    <use xlink:href="#DejaVuSans-63" x="883.789062"/>
    <use xlink:href="#DejaVuSans-74" x="938.769531"/>
    <use xlink:href="#DejaVuSans-75" x="977.978516"/>
    <use xlink:href="#DejaVuSans-72" x="1041.357422"/>
    <use xlink:href="#DejaVuSans-65" x="1080.220703"/>
    <use xlink:href="#DejaVuSans-20" x="1141.744141"/>
    <use xlink:href="#DejaVuSans-61" x="1173.53125"/>
    <use xlink:href="#DejaVuSans-6e" x="1234.810547"/>
    <use xlink:href="#DejaVuSans-64" x="1298.189453"/>
    <use xlink:href="#DejaVuSans-20" x="1361.666016"/>
    <use xlink:href="#DejaVuSans-44" x="1393.453125"/>
    <use xlink:href="#DejaVuSans-4f" x="1470.455078"/>
    <use xlink:href="#DejaVuSans-53" x="1549.166016"/>
    <use xlink:href="#DejaVuSans-20" x="1612.642578"/>
    <use xlink:href="#DejaVuSans-6f" x="1644.429688"/>
    <use xlink:href="#DejaVuSans-66" x="1705.611328"/>
    <use xlink:href="#DejaVuSans-20" x="1740.816406"/>
    <use xlink:href="#DejaVuSans-46" x="1772.603516"/>
    <use xlink:href="#DejaVuSans-4c" x="1830.123047"/>
    <use xlink:href="#DejaVuSans-69" x="1885.835938"/>
    <use xlink:href="#DejaVuSans-20" x="1913.619141"/>
    <use xlink:href="#DejaVuSans-28" x="1945.40625"/>
    <use xlink:href="#DejaVuSans-73" x="1984.419922"/>
    <use xlink:href="#DejaVuSans-69" x="2036.519531"/>
    <use xlink:href="#DejaVuSans-6d" x="2064.302734"/>
    <use xlink:href="#DejaVuSans-70" x="2161.714844"/>
    <use xlink:href="#DejaVuSans-6c" x="2225.191406"/>
    <use xlink:href="#DejaVuSans-65" x="2252.974609"/>
    <use xlink:href="#DejaVuSans-5f" x="2314.498047"/>
    <use xlink:href="#DejaVuSans-63" x="2364.498047"/>
    <use xlink:href="#DejaVuSans-75" x="2419.478516"/>
    <use xlink:href="#DejaVuSans-62" x="2482.857422"/>
    <use xlink:href="#DejaVuSans-69" x="2546.333984"/>
    <use xlink:href="#DejaVuSans-63" x="2574.117188"/>
    <use xlink:href="#DejaVuSans-29" x="2629.097656"/>
    <use xlink:href="#DejaVuSans-20" x="2668.111328"/>
    <use xlink:href="#DejaVuSans-77" x="2699.898438"/>
    <use xlink:href="#DejaVuSans-69" x="2781.685547"/>
    <use xlink:href="#DejaVuSans-74" x="2809.46875"/>
    <use xlink:href="#DejaVuSans-68" x="2848.677734"/>
    <use xlink:href="#DejaVuSans-20" x="2912.056641"/>
    <use xlink:href="#DejaVuSans-28" x="2943.84375"/>
    <use xlink:href="#DejaVuSans-34" x="2982.857422"/>
    <use xlink:href="#DejaVuSans-78" x="3046.480469"/>
    <use xlink:href="#DejaVuSans-34" x="3105.660156"/>
    <use xlink:href="#DejaVuSans-78" x="3169.283203"/>
    <use xlink:href="#DejaVuSans-34" x="3228.462891"/>
    <use xlink:href="#DejaVuSans-29" x="3292.085938"/>
    <use xlink:href="#DejaVuSans-20" x="3331.099609"/>
    <use xlink:href="#DejaVuSans-73" x="3362.886719"/>
    <use xlink:href="#DejaVuSans-75" x="3414.986328"/>
    <use xlink:href="#DejaVuSans-70" x="3478.365234"/>
    <use xlink:href="#DejaVuSans-65" x="3541.841797"/>
    <use xlink:href="#DejaVuSans-72" x="3603.365234"/>
    <use xlink:href="#DejaVuSans-63" x="3642.228516"/>
    <use xlink:href="#DejaVuSans-65" x="3697.208984"/>
    <use xlink:href="#DejaVuSans-6c" x="3758.732422"/>
    <use xlink:href="#DejaVuSans-6c" x="3786.515625"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pc6c6e130e5">
   <rect x="48.633125" y="36" width="337.68" height="244.8"/>
  </clipPath>
  <clipPath id="p0e9486cf16">
   <rect x="391.353125" y="36" width="85.68" height="244.8"/>
  </clipPath>
 </defs>
</svg>
